/**
 * SM加密功能测试
 * 测试基础功能和状态检查
 */

import {
  storePrivateKey,
  getEncryptionStatus,
  isEncryptionReady
} from '../app/utils/sm-crypto';

import { getDeviceNo, clearDeviceNo } from '../app/utils/device';

describe('SM加密功能测试', () => {
  beforeEach(() => {
    // 每个测试前清除设备号，确保测试独立性
    clearDeviceNo();
  });

  describe('基础功能测试', () => {
    test('应该能够存储私钥', () => {
      const testPrivateKey = 'test_private_key_12345';

      // 存储私钥不应该抛出错误
      expect(() => {
        storePrivateKey(testPrivateKey);
      }).not.toThrow();
    });

    test('应该能够检查加密环境状态', () => {
      const status = getEncryptionStatus();

      // 验证状态对象结构
      expect(status).toHaveProperty('sm2Available');
      expect(status).toHaveProperty('sm4Available');
      expect(status).toHaveProperty('privateKeyAvailable');
      expect(status).toHaveProperty('deviceNoAvailable');
      expect(status).toHaveProperty('ready');

      // 验证属性类型
      expect(typeof status.sm2Available).toBe('boolean');
      expect(typeof status.sm4Available).toBe('boolean');
      expect(typeof status.privateKeyAvailable).toBe('boolean');
      expect(typeof status.deviceNoAvailable).toBe('boolean');
      expect(typeof status.ready).toBe('boolean');
    });

    test('应该能够检查加密是否就绪', () => {
      const ready = isEncryptionReady();
      expect(typeof ready).toBe('boolean');
    });

    test('设备号生成后应该影响加密状态', () => {
      // 获取设备号（会自动生成）
      const deviceNo = getDeviceNo();
      expect(deviceNo).toBeTruthy();
      expect(typeof deviceNo).toBe('string');

      // 检查状态
      const status = getEncryptionStatus();
      expect(status.deviceNoAvailable).toBe(true);
    });

    test('存储私钥后应该影响加密状态', () => {
      const testPrivateKey = 'test_private_key_for_status_check';

      // 存储私钥
      storePrivateKey(testPrivateKey);

      // 检查状态
      const status = getEncryptionStatus();
      expect(status.privateKeyAvailable).toBe(true);
    });
  });

  describe('设备和私钥集成测试', () => {
    test('完整的设备号和私钥管理流程', () => {
      // 1. 生成设备号
      const deviceNo = getDeviceNo();
      expect(deviceNo).toBeTruthy();

      // 2. 存储私钥
      const testPrivateKey = 'integration_test_private_key';
      storePrivateKey(testPrivateKey);

      // 3. 检查最终状态
      const status = getEncryptionStatus();
      expect(status.deviceNoAvailable).toBe(true);
      expect(status.privateKeyAvailable).toBe(true);

      // 4. 检查整体就绪状态
      const ready = isEncryptionReady();
      // 注意：在测试环境中，sm2和sm4可能不可用，所以ready可能为false
      expect(typeof ready).toBe('boolean');
    });
  });
});
