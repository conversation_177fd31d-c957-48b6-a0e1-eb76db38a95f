/**
 * 测试 GET 请求加密流程
 */

console.log('🧪 测试 GET 请求加密流程...');

// 模拟 buildEncryptedRequest 函数
function mockBuildEncryptedRequest(data) {
  console.log('🔒 GET请求加密处理被调用，原始数据:', data);
  return {
    deviceNo: 'test_device_123',
    applicationType: 6,
    osType: 1,
    appVersion: '1.0.0',
    sign: 'mock_signature_12345',
    reqData: `encrypted_${JSON.stringify(data || '')}`
  };
}

// 模拟 processRequestData 函数
function mockProcessRequestData(data, config) {
  console.log('📝 processRequestData 被调用');
  console.log('   - 原始数据:', data);
  console.log('   - 配置:', config);
  
  // 默认启用加密，除非明确设置为false
  const shouldEncrypt = config.withEncryption !== false;
  console.log('   - 是否加密:', shouldEncrypt);
  
  if (shouldEncrypt) {
    const result = mockBuildEncryptedRequest(data);
    console.log('   - 加密结果:', result);
    return result;
  }
  
  console.log('   - 不加密，返回原始数据');
  return data;
}

// 模拟 GET 请求处理
function mockGet(endpoint, params, config = {}) {
  console.log('\n📡 GET 请求开始');
  console.log('   - 端点:', endpoint);
  console.log('   - 原始参数:', params);
  console.log('   - 配置:', config);
  
  // 先处理参数加密
  const processedData = mockProcessRequestData(params, config);
  console.log('   - 处理后数据:', processedData);
  
  let url = endpoint;
  if (processedData) {
    const searchParams = new URLSearchParams();
    Object.entries(processedData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    url += `?${searchParams.toString()}`;
  }
  
  console.log('   - 最终URL:', url);
  
  return { success: true, url };
}

// 测试用例
console.log('\n=== 测试用例 1: GET请求带参数（默认加密） ===');
mockGet('/user/profile', { userId: 123, type: 'full' });

console.log('\n=== 测试用例 2: GET请求无参数（默认加密） ===');
mockGet('/user/info');

console.log('\n=== 测试用例 3: GET请求明确不加密 ===');
mockGet('/public/data', { page: 1, size: 10 }, { withEncryption: false });

console.log('\n=== 测试用例 4: GET请求空参数（默认加密） ===');
mockGet('/auth/status', {});

console.log('\n✅ GET请求加密测试完成');
