/**
 * Custom API 演示组件
 * 展示如何使用新的 Custom API 服务
 */

"use client";

import React, { useState, useEffect } from 'react';
import { 
  customApi, 
  authApi, 
  userApi, 
  chatApi,
  type AuthLoginRequest,
  type CreateSessionRequest,
  type SendMessageRequest 
} from '../api/custom-api';

interface DemoState {
  encryptionReady: boolean;
  encryptionStatus: any;
  loginResult: any;
  userProfile: any;
  chatSession: any;
  chatResponse: any;
  loading: boolean;
  error: string;
}

export function CustomApiDemo() {
  const [state, setState] = useState<DemoState>({
    encryptionReady: false,
    encryptionStatus: null,
    loginResult: null,
    userProfile: null,
    chatSession: null,
    chatResponse: null,
    loading: false,
    error: ''
  });

  useEffect(() => {
    checkEncryptionStatus();
  }, []);

  const checkEncryptionStatus = () => {
    try {
      const ready = customApi.isReady();
      const status = customApi.getStatus();
      
      setState(prev => ({
        ...prev,
        encryptionReady: ready,
        encryptionStatus: status
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `检查加密状态失败: ${error instanceof Error ? error.message : String(error)}`
      }));
    }
  };

  const testLogin = async () => {
    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      const loginData: AuthLoginRequest = {
        username: '<EMAIL>',
        password: 'demo123456',
        captcha: '1234'
      };

      const result = await authApi.login(loginData);
      
      setState(prev => ({
        ...prev,
        loginResult: result,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `登录测试失败: ${error instanceof Error ? error.message : String(error)}`,
        loading: false
      }));
    }
  };

  const testGetProfile = async () => {
    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      const profile = await userApi.getProfile();
      
      setState(prev => ({
        ...prev,
        userProfile: profile,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `获取用户资料失败: ${error instanceof Error ? error.message : String(error)}`,
        loading: false
      }));
    }
  };

  const testCreateSession = async () => {
    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      const sessionData: CreateSessionRequest = {
        title: '演示对话',
        model: 'gpt-4',
        systemPrompt: '你是一个有用的AI助手',
        tags: ['演示', 'API测试']
      };

      const session = await chatApi.createSession(sessionData);
      
      setState(prev => ({
        ...prev,
        chatSession: session,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `创建会话失败: ${error instanceof Error ? error.message : String(error)}`,
        loading: false
      }));
    }
  };

  const testSendMessage = async () => {
    if (!state.chatSession) {
      setState(prev => ({
        ...prev,
        error: '请先创建聊天会话'
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      const messageData: SendMessageRequest = {
        sessionId: state.chatSession.id,
        content: '你好，请介绍一下自己',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 500
      };

      const response = await chatApi.sendMessage(messageData);
      
      setState(prev => ({
        ...prev,
        chatResponse: response,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `发送消息失败: ${error instanceof Error ? error.message : String(error)}`,
        loading: false
      }));
    }
  };

  const testPlainApi = async () => {
    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      // 测试不加密的API调用
      const response = await customApi.postPlain('/public/test', {
        message: '这是一个不加密的测试请求'
      });
      
      setState(prev => ({
        ...prev,
        error: `不加密API测试成功: ${JSON.stringify(response)}`,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `不加密API测试: ${error instanceof Error ? error.message : String(error)}`,
        loading: false
      }));
    }
  };

  const testEncryptedApi = async () => {
    setState(prev => ({ ...prev, loading: true, error: '' }));
    
    try {
      // 测试强制加密的API调用
      const response = await customApi.postEncrypted('/secure/test', {
        message: '这是一个加密的测试请求',
        timestamp: Date.now()
      });
      
      setState(prev => ({
        ...prev,
        error: `加密API测试成功: ${JSON.stringify(response)}`,
        loading: false
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `加密API测试: ${error instanceof Error ? error.message : String(error)}`,
        loading: false
      }));
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Custom API 演示</h1>
      
      {/* 加密状态 */}
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
        <h3>加密环境状态</h3>
        <p>加密就绪: {state.encryptionReady ? '✅ 是' : '❌ 否'}</p>
        {state.encryptionStatus && (
          <div>
            <p>SM2可用: {state.encryptionStatus.sm2Available ? '✅' : '❌'}</p>
            <p>SM4可用: {state.encryptionStatus.sm4Available ? '✅' : '❌'}</p>
            <p>私钥可用: {state.encryptionStatus.privateKeyAvailable ? '✅' : '❌'}</p>
            <p>设备号可用: {state.encryptionStatus.deviceNoAvailable ? '✅' : '❌'}</p>
          </div>
        )}
        <button onClick={checkEncryptionStatus} disabled={state.loading}>
          刷新状态
        </button>
      </div>

      {/* 测试按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <h3>API测试</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button onClick={testLogin} disabled={state.loading}>
            测试登录API
          </button>
          <button onClick={testGetProfile} disabled={state.loading}>
            测试获取用户资料
          </button>
          <button onClick={testCreateSession} disabled={state.loading}>
            测试创建聊天会话
          </button>
          <button onClick={testSendMessage} disabled={state.loading}>
            测试发送消息
          </button>
          <button onClick={testPlainApi} disabled={state.loading}>
            测试不加密API
          </button>
          <button onClick={testEncryptedApi} disabled={state.loading}>
            测试加密API
          </button>
        </div>
      </div>

      {/* 加载状态 */}
      {state.loading && (
        <div style={{ padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px', marginBottom: '20px' }}>
          正在处理请求...
        </div>
      )}

      {/* 错误信息 */}
      {state.error && (
        <div style={{ padding: '10px', backgroundColor: '#ffe6e6', borderRadius: '5px', marginBottom: '20px', color: '#d00' }}>
          {state.error}
        </div>
      )}

      {/* 结果显示 */}
      {state.loginResult && (
        <div style={{ marginBottom: '20px' }}>
          <h4>登录结果:</h4>
          <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '5px', overflow: 'auto' }}>
            {JSON.stringify(state.loginResult, null, 2)}
          </pre>
        </div>
      )}

      {state.userProfile && (
        <div style={{ marginBottom: '20px' }}>
          <h4>用户资料:</h4>
          <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '5px', overflow: 'auto' }}>
            {JSON.stringify(state.userProfile, null, 2)}
          </pre>
        </div>
      )}

      {state.chatSession && (
        <div style={{ marginBottom: '20px' }}>
          <h4>聊天会话:</h4>
          <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '5px', overflow: 'auto' }}>
            {JSON.stringify(state.chatSession, null, 2)}
          </pre>
        </div>
      )}

      {state.chatResponse && (
        <div style={{ marginBottom: '20px' }}>
          <h4>聊天响应:</h4>
          <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '5px', overflow: 'auto' }}>
            {JSON.stringify(state.chatResponse, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
