"use client";

import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import styles from "./recharge.module.scss";
import { IconButton } from "./button";
import { Card, Modal, showModal, showToast } from "./ui-lib";
import Locale from "../locales";
import { Path } from "../constant";
import {
  StripeApi,
  RechargePackage,
  RECHARGE_PACKAGES,
  PaymentRequest,
  calculateValueRatio,
  formatCoins,
  UserBalance,
  formatPrice,
  createCustomPackage,
  validateCustomAmount,
  calculateCoinsFromUSD,
} from "../api/custom-api";
import { isAuthenticated } from "../utils/auth-token";
import { PaymentElementWrapper } from "./payment-element";

// 支付处理函数 - 现在使用 Payment Element 模式
const handlePaymentSuccess = (
  setShowSuccessModal: (show: boolean) => void,
  loadUserBalance: () => void,
) => {
  setShowSuccessModal(true);
  loadUserBalance(); // 重新加载余额
};

const handlePaymentError = (
  error: string,
  setShowFailModal: (show: boolean) => void,
  setFailMessage: (message: string) => void,
) => {
  setFailMessage(error);
  setShowFailModal(true);
};

// 主充值页面组件
export function RechargePage() {
  const navigate = useNavigate();

  // 检查用户是否已登录
  useEffect(() => {
    if (!isAuthenticated()) {
      navigate(Path.Login);
      return;
    }
  }, [navigate]);

  const [searchParams] = useSearchParams();
  const [selectedPackage, setSelectedPackage] =
    useState<RechargePackage | null>(() => {
      // 默认选中标准套餐
      return RECHARGE_PACKAGES.find((pkg) => pkg.id === "popular") || null;
    });
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showFailModal, setShowFailModal] = useState(false);
  const [failMessage, setFailMessage] = useState("");
  const [customAmount, setCustomAmount] = useState<string>("");
  const [customAmountError, setCustomAmountError] = useState<string>("");

  // 加载用户余额
  const loadUserBalance = async () => {
    try {
      setIsLoading(true);
      const data: any = await StripeApi.getUserBalance();
      const result = data?.rows[0] || {};
      const balance = {
        coins: result.coinBalance || 0,
      };
      setUserBalance(balance);
    } catch (error: any) {
      console.error("Load balance error:", error);
      // 如果获取余额失败，设置默认值
      setUserBalance({ coins: 0 });
    } finally {
      setIsLoading(false);
    }
  };

  // 处理支付结果
  useEffect(() => {
    const success = searchParams.get("success");
    const canceled = searchParams.get("canceled");

    if (success === "true") {
      // 验证支付结果
      const verifyPayment = () => {
        setShowSuccessModal(true);
        // 清理URL参数
        navigate("/recharge", { replace: true });
      };

      verifyPayment();
    } else if (canceled === "true") {
      setFailMessage(Locale.Recharge.PaymentCanceled);
      setShowFailModal(true);
      // 清理URL参数
      navigate("/recharge", { replace: true });
    }
  }, [searchParams, navigate]);

  useEffect(() => {
    loadUserBalance();
  }, []);

  const handlePackageSelect = (pkg: RechargePackage) => {
    setError(null);
    // 如果选择的不是自定义套餐，清空自定义金额
    if (pkg.id !== "custom") {
      setCustomAmount("");
      setCustomAmountError("");
      setSelectedPackage(pkg); // 确保设置为新选择的套餐
    } else {
      // 如果选择自定义套餐，只有在有有效输入时才设置
      if (customAmount && !isNaN(parseFloat(customAmount))) {
        const numValue = parseFloat(customAmount);
        const validation = validateCustomAmount(numValue);
        if (validation.valid) {
          const customPkg = createCustomPackage(numValue);
          setSelectedPackage(customPkg);
        } else {
          setSelectedPackage(null);
        }
      } else {
        setSelectedPackage(null);
      }
    }
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setCustomAmountError("");

    // 实时验证和更新自定义套餐
    const numValue = parseFloat(value);
    if (value && !isNaN(numValue)) {
      const validation = validateCustomAmount(numValue);
      if (validation.valid) {
        const customPkg = createCustomPackage(numValue);
        setSelectedPackage(customPkg);
      } else {
        setCustomAmountError(validation.message || "");
      }
    } else if (selectedPackage?.id === "custom") {
      // 如果当前选中的是自定义套餐但输入为空，清空选择
      setSelectedPackage(null);
    }
  };

  const handlePaymentClick = () => {
    if (!selectedPackage) {
      setError(Locale.Recharge.Payment.SelectPackage);
      return;
    }
    setShowPaymentForm(true);
  };

  const goBack = () => {
    navigate(Path.Home);
  };

  if (isLoading) {
    return (
      <div className={styles["recharge-page"]}>
        <div className={styles["page-header"]}>
          <div className={styles["header-left"]}>
            <div className={styles["page-title"]}>
              <h1>{Locale.Recharge.Title}</h1>
              <p>{Locale.Recharge.SubTitle}</p>
            </div>
            <div className={styles["header-actions"]}>
              <button
                className={styles["history-button"]}
                onClick={() => navigate(Path.RechargeHistory)}
              >
                充值记录
              </button>
            </div>
          </div>
          <div
            className={styles["close-button"]}
            onClick={() => navigate(Path.Home)}
          >
            ✕
          </div>
        </div>
        <div className={styles["page-content"]}>
          <div className={styles["loading-container"]}>
            <div className={styles["loading-spinner"]}></div>
            <div className={styles["loading-text"]}>正在加载充值信息...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles["recharge-page"]}>
      <div className={styles["page-header"]}>
        <div className={styles["header-left"]}>
          <div className={styles["page-title"]}>
            <h1>{Locale.Recharge.Title}</h1>
            <p>{Locale.Recharge.SubTitle}</p>
          </div>
          <div className={styles["header-actions"]}>
            <button
              className={styles["history-button"]}
              onClick={() => navigate(Path.RechargeHistory)}
            >
              充值记录
            </button>
          </div>
        </div>
        <div
          className={styles["close-button"]}
          onClick={() => navigate(Path.Home)}
        >
          ✕
        </div>
      </div>

      <div className={styles["page-content"]}>
        <div className={styles["packages-container"]}>
          <div className={styles["packages-header"]}>
            <div className={styles["packages-title"]}>
              {Locale.Recharge.CoinPackages.Title}
            </div>
            <div className={styles["packages-subtitle"]}>
              {Locale.Recharge.CoinPackages.SubTitle}
            </div>
          </div>

          <div className={styles["packages-grid"]}>
            {RECHARGE_PACKAGES.map((pkg) => {
              if (pkg.isCustom) {
                return (
                  <div
                    key={pkg.id}
                    className={`${styles["package-card"]} ${
                      styles["custom-package"]
                    } ${
                      selectedPackage?.id === pkg.id ? styles["selected"] : ""
                    }`}
                    onClick={() => handlePackageSelect(pkg)}
                  >
                    <div className={styles["package-header"]}>
                      <div className={styles["package-title"]}>{pkg.title}</div>
                      <div className={styles["package-description"]}>
                        {pkg.description}
                      </div>
                    </div>
                    <div className={styles["package-content"]}>
                      <div className={styles["custom-input-container"]}>
                        <div className={styles["input-wrapper"]}>
                          <span className={styles["currency-symbol"]}>$</span>
                          <input
                            type="number"
                            className={styles["custom-amount-input"]}
                            placeholder="输入金额"
                            value={customAmount}
                            onChange={(e) =>
                              handleCustomAmountChange(e.target.value)
                            }
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePackageSelect(pkg);
                            }}
                            min="1"
                            max="1000"
                            step="0.01"
                          />
                        </div>
                        {customAmountError && (
                          <div className={styles["custom-error"]}>
                            {customAmountError}
                          </div>
                        )}
                        {customAmount && !customAmountError && (
                          <div className={styles["custom-preview"]}>
                            获得{" "}
                            {formatCoins(
                              calculateCoinsFromUSD(parseFloat(customAmount)),
                            )}{" "}
                            创想值
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              }

              return (
                <div
                  key={pkg.id}
                  className={`${styles["package-card"]} ${
                    pkg.popular ? styles["popular"] : ""
                  } ${
                    selectedPackage?.id === pkg.id ? styles["selected"] : ""
                  }`}
                  onClick={() => handlePackageSelect(pkg)}
                >
                  <div className={styles["package-header"]}>
                    <div className={styles["package-title"]}>{pkg.title}</div>
                    <div className={styles["package-description"]}>
                      {pkg.description}
                    </div>
                  </div>
                  <div className={styles["package-content"]}>
                    <div className={styles["coins-amount"]}>
                      {formatCoins(pkg.coins)}
                      <span className={styles["coins-label"]}>
                        {Locale.Recharge.Coins}
                      </span>
                    </div>
                    <div className={styles["price"]}>
                      <span className={styles["currency"]}>$</span>
                      {pkg.price}
                    </div>
                    {/* <div className={styles["value-info"]}>
                      约 {Math.round((pkg.coins / pkg.price) * 10) / 10}{" "}
                      创想值/美元
                    </div> */}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 支付区域 */}
        {selectedPackage && (
          <div className={styles["payment-section"]}>
            {error && <div className={styles["error-message"]}>{error}</div>}

            <div className={styles["selected-package"]}>
              <div className={styles["selected-title"]}>
                已选择：{selectedPackage.title}
              </div>
              <div className={styles["selected-details"]}>
                {formatCoins(selectedPackage.coins)} 创想值 -{" "}
                {formatPrice(selectedPackage.price)}
              </div>
            </div>

            <button
              className={styles["payment-button"]}
              onClick={handlePaymentClick}
              disabled={showPaymentForm}
            >
              {showPaymentForm ? (
                <>
                  <span className={styles["loading-spinner"]}></span>
                  正在初始化支付...
                </>
              ) : (
                `${Locale.Recharge.Payment.PayNow} ${formatPrice(
                  selectedPackage.price,
                )}`
              )}
            </button>

            <div className={styles["security-info"]}>
              <svg
                className={styles["security-icon"]}
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z" />
              </svg>
              {Locale.Recharge.Payment.SecurePayment}
            </div>
          </div>
        )}

        {/* 支付表单模态框 */}
        {showPaymentForm && selectedPackage && (
          <div className={styles["modal-overlay"]}>
            <div className={styles["payment-modal"]}>
              <PaymentElementWrapper
                selectedPackage={selectedPackage}
                onSuccess={() => {
                  setShowPaymentForm(false);
                  handlePaymentSuccess(setShowSuccessModal, loadUserBalance);
                }}
                onError={(error) => {
                  setShowPaymentForm(false);
                  handlePaymentError(error, setShowFailModal, setFailMessage);
                }}
                onCancel={() => setShowPaymentForm(false)}
              />
            </div>
          </div>
        )}

        {/* 支付成功模态框 */}
        {showSuccessModal && (
          <div className={styles["modal-overlay"]}>
            <div className={styles["success-modal"]}>
              <div className={styles["modal-header"]}>
                <div className={styles["success-icon"]}>
                  <svg viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" fill="#4CAF50" />
                    <path
                      d="M9 12l2 2 4-4"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <h3 className={styles["modal-title"]}>
                  {Locale.Recharge.Payment.Success}
                </h3>
                <button
                  className={styles["modal-close"]}
                  onClick={() => setShowSuccessModal(false)}
                >
                  ✕
                </button>
              </div>
              <div className={styles["modal-content"]}>
                <p className={styles["success-message"]}>
                  {Locale.Recharge.Payment.SuccessMessage}
                </p>
                <div className={styles["success-details"]}>
                  <div className={styles["detail-item"]}>
                    <span className={styles["detail-label"]}>充值套餐：</span>
                    <span className={styles["detail-value"]}>
                      {selectedPackage?.title}
                    </span>
                  </div>
                  <div className={styles["detail-item"]}>
                    <span className={styles["detail-label"]}>获得创想值：</span>
                    <span className={styles["detail-value"]}>
                      {selectedPackage
                        ? formatCoins(selectedPackage.coins)
                        : ""}
                    </span>
                  </div>
                </div>
              </div>
              <div className={styles["modal-footer"]}>
                <button
                  className={styles["success-button"]}
                  onClick={() => {
                    setShowSuccessModal(false);
                    loadUserBalance(); // 重新加载余额
                  }}
                >
                  确定
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 支付失败模态框 */}
        {showFailModal && (
          <div className={styles["modal-overlay"]}>
            <div className={styles["fail-modal"]}>
              <div className={styles["modal-header"]}>
                <div className={styles["fail-icon"]}>
                  <svg viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" fill="#F44336" />
                    <path
                      d="M15 9l-6 6M9 9l6 6"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <h3 className={styles["modal-title"]}>支付失败</h3>
                <button
                  className={styles["modal-close"]}
                  onClick={() => setShowFailModal(false)}
                >
                  ✕
                </button>
              </div>
              <div className={styles["modal-content"]}>
                <p className={styles["fail-message"]}>{failMessage}</p>
                <div className={styles["fail-tips"]}>
                  <h4>可能的原因：</h4>
                  <ul>
                    <li>网络连接不稳定</li>
                    <li>支付信息填写错误</li>
                    <li>银行卡余额不足</li>
                    <li>支付服务暂时不可用</li>
                  </ul>
                </div>
              </div>
              <div className={styles["modal-footer"]}>
                <button
                  className={styles["fail-button-secondary"]}
                  onClick={() => setShowFailModal(false)}
                >
                  取消
                </button>
                <button
                  className={styles["fail-button-primary"]}
                  onClick={() => {
                    setShowFailModal(false);
                    if (selectedPackage) {
                      handlePaymentClick();
                    }
                  }}
                >
                  重试支付
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
