/**
 * 设备号管理演示组件
 */

import React, { useState, useEffect } from 'react';
import { getDeviceNo, regenerateDeviceNo, clearDeviceNo, hasDeviceNo } from '../utils/device';
import { buildApplyPrivateKeyRequest } from '../api/custom-api/key-api';

interface DeviceDemoProps {
  className?: string;
}

export function DeviceDemo({ className }: DeviceDemoProps) {
  const [currentDeviceNo, setCurrentDeviceNo] = useState<string>('');
  const [hasDevice, setHasDevice] = useState<boolean>(false);
  const [requestData, setRequestData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 更新设备状态
  const updateDeviceStatus = () => {
    const deviceExists = hasDeviceNo();
    setHasDevice(deviceExists);
    if (deviceExists) {
      setCurrentDeviceNo(getDeviceNo());
    } else {
      setCurrentDeviceNo('');
    }
  };

  useEffect(() => {
    updateDeviceStatus();
  }, []);

  // 获取设备号
  const handleGetDeviceNo = () => {
    const deviceNo = getDeviceNo();
    setCurrentDeviceNo(deviceNo);
    updateDeviceStatus();
  };

  // 重新生成设备号
  const handleRegenerateDeviceNo = () => {
    const newDeviceNo = regenerateDeviceNo();
    setCurrentDeviceNo(newDeviceNo);
    updateDeviceStatus();
    // 清除之前的请求数据
    setRequestData(null);
  };

  // 清除设备号
  const handleClearDeviceNo = () => {
    clearDeviceNo();
    setCurrentDeviceNo('');
    updateDeviceStatus();
    setRequestData(null);
  };

  // 生成请求参数
  const handleGenerateRequest = async () => {
    setIsLoading(true);
    try {
      const request = buildApplyPrivateKeyRequest();
      setRequestData(request);
      updateDeviceStatus();
    } catch (error) {
      console.error('生成请求参数失败:', error);
      alert('生成请求参数失败: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`device-demo ${className || ''}`}>
      <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', margin: '20px' }}>
        <h3>设备号管理演示</h3>
        
        {/* 设备状态显示 */}
        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
          <p><strong>设备状态:</strong> {hasDevice ? '已有设备号' : '无设备号'}</p>
          {currentDeviceNo && (
            <p><strong>当前设备号:</strong> <code>{currentDeviceNo}</code></p>
          )}
        </div>

        {/* 操作按钮 */}
        <div style={{ marginBottom: '20px' }}>
          <button 
            onClick={handleGetDeviceNo}
            style={{ marginRight: '10px', padding: '8px 16px', cursor: 'pointer' }}
          >
            获取设备号
          </button>
          
          <button 
            onClick={handleRegenerateDeviceNo}
            style={{ marginRight: '10px', padding: '8px 16px', cursor: 'pointer' }}
          >
            重新生成设备号
          </button>
          
          <button 
            onClick={handleClearDeviceNo}
            style={{ marginRight: '10px', padding: '8px 16px', cursor: 'pointer' }}
          >
            清除设备号
          </button>
          
          <button 
            onClick={handleGenerateRequest}
            disabled={isLoading}
            style={{ padding: '8px 16px', cursor: isLoading ? 'not-allowed' : 'pointer' }}
          >
            {isLoading ? '生成中...' : '生成API请求参数'}
          </button>
        </div>

        {/* 请求参数显示 */}
        {requestData && (
          <div style={{ marginTop: '20px' }}>
            <h4>生成的API请求参数:</h4>
            <div style={{ 
              backgroundColor: '#f8f8f8', 
              padding: '15px', 
              borderRadius: '4px', 
              border: '1px solid #ddd',
              fontFamily: 'monospace',
              fontSize: '12px',
              overflow: 'auto'
            }}>
              <div><strong>deviceNo:</strong> {requestData.deviceNo}</div>
              <div style={{ marginTop: '8px' }}>
                <strong>masterKey:</strong> 
                <div style={{ wordBreak: 'break-all', marginTop: '4px' }}>
                  {requestData.masterKey}
                </div>
              </div>
              <div style={{ marginTop: '8px' }}>
                <strong>sign:</strong> 
                <div style={{ wordBreak: 'break-all', marginTop: '4px' }}>
                  {requestData.sign}
                </div>
              </div>
            </div>
            
            <div style={{ marginTop: '10px' }}>
              <button 
                onClick={() => {
                  navigator.clipboard.writeText(JSON.stringify(requestData, null, 2));
                  alert('请求参数已复制到剪贴板');
                }}
                style={{ padding: '6px 12px', cursor: 'pointer', fontSize: '12px' }}
              >
                复制JSON
              </button>
            </div>
          </div>
        )}

        {/* 说明文档 */}
        <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
          <h4>功能说明:</h4>
          <ul>
            <li><strong>获取设备号:</strong> 如果本地没有设备号则自动生成，有则直接返回</li>
            <li><strong>重新生成设备号:</strong> 强制生成新的设备号并覆盖原有的</li>
            <li><strong>清除设备号:</strong> 从本地存储中删除设备号</li>
            <li><strong>生成API请求参数:</strong> 根据当前设备号生成完整的API请求参数</li>
          </ul>
          
          <h4>加密流程:</h4>
          <ol>
            <li>对MASTER_KEY进行base64编码</li>
            <li>使用SM2加密base64后的数据</li>
            <li>对加密结果进行C1C3C2处理</li>
            <li>在结果前添加&quot;04&quot;前缀得到masterKey</li>
            <li>使用SM4加密deviceNo得到sign值</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
