import React, { useState, useRef, useCallback } from "react";
import { Icon<PERSON>utton } from "./button";
import { useNavigate } from "react-router-dom";
import styles from "./universal-generate.module.scss";

import BackIcon from "../icons/back.svg";
import UploadIcon from "../icons/upload.svg";
import ImageIcon from "../icons/image.svg";
import LightningIcon from "../icons/lightning.svg";

interface GenerateResult {
  id: string;
  imageUrl: string;
  prompt: string;
  timestamp: number;
}

export function UniversalGenerate() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"instant" | "results">("instant");
  const [referenceImage, setReferenceImage] = useState<string | null>(null);
  const [backgroundImage, setBackgroundImage] = useState<string | null>(null);
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [results, setResults] = useState<GenerateResult[]>([]);
  const [freeUsage, setFreeUsage] = useState(3);

  const referenceInputRef = useRef<HTMLInputElement>(null);
  const backgroundInputRef = useRef<HTMLInputElement>(null);

  const handleBack = () => {
    navigate(-1);
  };

  const handleReferenceUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setReferenceImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleBackgroundUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setBackgroundImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    
    // 模拟生成过程
    setTimeout(() => {
      const newResult: GenerateResult = {
        id: Date.now().toString(),
        imageUrl: "/api/placeholder/512/512", // 占位图片
        prompt: prompt,
        timestamp: Date.now()
      };
      
      setResults(prev => [newResult, ...prev]);
      setFreeUsage(prev => Math.max(0, prev - 1));
      setIsGenerating(false);
      setActiveTab("results");
    }, 3000);
  };

  const examples = [
    {
      title: "人物肖像",
      description: "生成高质量人物肖像",
      image: "/api/placeholder/200/150",
      prompt: "一位优雅的女性，穿着白色连衣裙，在花园中微笑"
    },
    {
      title: "风景画",
      description: "创造美丽的自然风景",
      image: "/api/placeholder/200/150",
      prompt: "夕阳下的山脉，金色的光芒洒在湖面上"
    },
    {
      title: "抽象艺术",
      description: "现代抽象艺术作品",
      image: "/api/placeholder/200/150",
      prompt: "色彩斑斓的抽象几何图形，充满活力"
    },
    {
      title: "动物世界",
      description: "可爱的动物形象",
      image: "/api/placeholder/200/150",
      prompt: "一只可爱的小猫咪，蓝色的眼睛，毛茸茸的"
    }
  ];

  return (
    <div className={styles.container}>
      {/* 头部导航 */}
      <div className={styles.header}>
        <IconButton
          icon={<BackIcon />}
          onClick={handleBack}
          className={styles.backButton}
        />
        <h1 className={styles.title}>万物生成</h1>
      </div>

      <div className={styles.content}>
        {/* 左侧面板 */}
        <div className={styles.leftPanel}>
          {/* Tab导航 */}
          <div className={styles.tabNav}>
            <button
              className={`${styles.tab} ${activeTab === "instant" ? styles.active : ""}`}
              onClick={() => setActiveTab("instant")}
            >
              立即使用
            </button>
            <button
              className={`${styles.tab} ${activeTab === "results" ? styles.active : ""}`}
              onClick={() => setActiveTab("results")}
            >
              生成结果
            </button>
          </div>

          {/* Tab内容 */}
          {activeTab === "instant" ? (
            <div className={styles.instantTab}>
              {/* 使用提示 */}
              <div className={styles.usageTip}>
                <span className={styles.tipIcon}>💡</span>
                <span>订阅作者后不限次数使用</span>
              </div>

              {/* 案例展示 */}
              <div className={styles.examplesSection}>
                <h3 className={styles.sectionTitle}>创作案例</h3>
                <div className={styles.exampleGrid}>
                  {examples.map((example, index) => (
                    <div
                      key={index}
                      className={styles.exampleCard}
                      onClick={() => setPrompt(example.prompt)}
                    >
                      <img src={example.image} alt={example.title} />
                      <div className={styles.exampleInfo}>
                        <h4>{example.title}</h4>
                        <p>{example.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className={styles.resultsTab}>
              {results.length === 0 ? (
                <div className={styles.emptyResults}>
                  <ImageIcon />
                  <p>暂无生成结果</p>
                  <span>开始创作您的第一个作品吧</span>
                </div>
              ) : (
                <div className={styles.resultGrid}>
                  {results.map((result) => (
                    <div key={result.id} className={styles.resultCard}>
                      <img src={result.imageUrl} alt="Generated" />
                      <div className={styles.resultInfo}>
                        <p className={styles.resultPrompt}>{result.prompt}</p>
                        <span className={styles.resultTime}>
                          {new Date(result.timestamp).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 右侧面板 */}
        <div className={styles.rightPanel}>
          {/* 参考图片上传 */}
          <div className={styles.uploadSection}>
            <label className={styles.uploadLabel}>
              添加自己产品图 (白底) <span className={styles.required}>*</span>
            </label>
            <div
              className={styles.uploadArea}
              onClick={() => referenceInputRef.current?.click()}
            >
              {referenceImage ? (
                <img src={referenceImage} alt="Reference" className={styles.uploadedImage} />
              ) : (
                <div className={styles.uploadPlaceholder}>
                  <div className={styles.uploadIcon}>
                    <UploadIcon />
                  </div>
                  <div className={styles.uploadText}>
                    拖拽文件到这里或点击上传
                  </div>
                </div>
              )}
            </div>
            <input
              ref={referenceInputRef}
              type="file"
              accept="image/*"
              onChange={handleReferenceUpload}
              style={{ display: "none" }}
            />
          </div>

          {/* 背景图片上传 */}
          <div className={styles.uploadSection}>
            <label className={styles.uploadLabel}>
              添加背景图 (局部重绘修改区域) <span className={styles.required}>*</span>
            </label>
            <div
              className={styles.uploadArea}
              onClick={() => backgroundInputRef.current?.click()}
            >
              {backgroundImage ? (
                <img src={backgroundImage} alt="Background" className={styles.uploadedImage} />
              ) : (
                <div className={styles.uploadPlaceholder}>
                  <div className={styles.uploadIcon}>
                    <UploadIcon />
                  </div>
                  <div className={styles.uploadText}>
                    拖拽文件到这里或点击上传
                  </div>
                </div>
              )}
            </div>
            <input
              ref={backgroundInputRef}
              type="file"
              accept="image/*"
              onChange={handleBackgroundUpload}
              style={{ display: "none" }}
            />
          </div>

          {/* 提示词输入 */}
          <div className={styles.promptSection}>
            <label className={styles.promptLabel}>描述您想要生成的内容</label>
            <textarea
              className={styles.promptInput}
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="请详细描述您想要生成的图像内容..."
              rows={4}
            />
          </div>

          {/* 生成按钮 */}
          <div className={styles.generateSection}>
            <button
              className={styles.generateButton}
              onClick={handleGenerate}
              disabled={!prompt.trim() || isGenerating}
            >
              {isGenerating ? "生成中..." : "Go"}
            </button>
            
            <div className={styles.usageInfo}>
              <div className={styles.freeUsage}>
                <LightningIcon />
                <span>免费试用 {freeUsage} 次</span>
              </div>
              <div className={styles.costInfo}>
                ≈8.1 ⚡ (共 148.60 算力) · 获取更多
              </div>
              <div className={styles.apiInfo}>
                该 AI 小工具支持 API 服务：查看 API 参数
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
