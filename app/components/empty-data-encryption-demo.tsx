/**
 * 空数据加密演示组件
 * 展示API没有请求参数值时也能正确走加密流程
 */

"use client";

import React, { useState } from 'react';
import { customApi } from '../api/custom-api';

interface DemoState {
  loading: boolean;
  results: Array<{
    type: string;
    description: string;
    success: boolean;
    data?: any;
    error?: string;
  }>;
}

export function EmptyDataEncryptionDemo() {
  const [state, setState] = useState<DemoState>({
    loading: false,
    results: []
  });

  const addResult = (result: DemoState['results'][0]) => {
    setState(prev => ({
      ...prev,
      results: [...prev.results, result]
    }));
  };

  const clearResults = () => {
    setState(prev => ({
      ...prev,
      results: []
    }));
  };

  const testEmptyStringEncryption = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 测试空字符串数据的加密
      const response = await customApi.postEncrypted('/test/empty-string', '');
      
      addResult({
        type: 'POST',
        description: '空字符串数据加密测试',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        type: 'POST',
        description: '空字符串数据加密测试',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const testNullDataEncryption = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 测试null数据的加密
      const response = await customApi.postEncrypted('/test/null-data', null);
      
      addResult({
        type: 'POST',
        description: 'null数据加密测试',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        type: 'POST',
        description: 'null数据加密测试',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const testUndefinedDataEncryption = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 测试undefined数据的加密
      const response = await customApi.postEncrypted('/test/undefined-data', undefined);
      
      addResult({
        type: 'POST',
        description: 'undefined数据加密测试',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        type: 'POST',
        description: 'undefined数据加密测试',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const testEmptyObjectEncryption = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 测试空对象数据的加密
      const response = await customApi.postEncrypted('/test/empty-object', {});
      
      addResult({
        type: 'POST',
        description: '空对象数据加密测试',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        type: 'POST',
        description: '空对象数据加密测试',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const testGetRequestEncryption = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 测试GET请求的加密（通常没有请求体）
      const response = await customApi.get('/test/get-request', undefined, {
        autoEncryption: true
      });
      
      addResult({
        type: 'GET',
        description: 'GET请求加密测试（无请求体）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        type: 'GET',
        description: 'GET请求加密测试（无请求体）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const testDeleteRequestEncryption = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 测试DELETE请求的加密（通常没有请求体）
      const response = await customApi.delete('/test/delete-request', {
        autoEncryption: true
      });
      
      addResult({
        type: 'DELETE',
        description: 'DELETE请求加密测试（无请求体）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        type: 'DELETE',
        description: 'DELETE请求加密测试（无请求体）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const runAllTests = async () => {
    clearResults();
    await testEmptyStringEncryption();
    await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
    await testNullDataEncryption();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testUndefinedDataEncryption();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testEmptyObjectEncryption();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testGetRequestEncryption();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testDeleteRequestEncryption();
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
      <h1>空数据加密演示</h1>
      <p>演示API没有请求参数值时也能正确走加密流程</p>
      
      {/* 控制按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          <button onClick={testEmptyStringEncryption} disabled={state.loading}>
            测试空字符串加密
          </button>
          <button onClick={testNullDataEncryption} disabled={state.loading}>
            测试null数据加密
          </button>
          <button onClick={testUndefinedDataEncryption} disabled={state.loading}>
            测试undefined数据加密
          </button>
          <button onClick={testEmptyObjectEncryption} disabled={state.loading}>
            测试空对象加密
          </button>
        </div>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          <button onClick={testGetRequestEncryption} disabled={state.loading}>
            测试GET请求加密
          </button>
          <button onClick={testDeleteRequestEncryption} disabled={state.loading}>
            测试DELETE请求加密
          </button>
        </div>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button onClick={runAllTests} disabled={state.loading}>
            运行所有测试
          </button>
          <button onClick={clearResults} disabled={state.loading}>
            清除结果
          </button>
        </div>
      </div>

      {/* 加载状态 */}
      {state.loading && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '5px', 
          marginBottom: '20px',
          textAlign: 'center'
        }}>
          正在测试加密功能...
        </div>
      )}

      {/* 测试结果 */}
      {state.results.length > 0 && (
        <div>
          <h3>测试结果</h3>
          <div style={{ display: 'grid', gap: '15px' }}>
            {state.results.map((result, index) => (
              <div
                key={index}
                style={{
                  padding: '15px',
                  border: `2px solid ${result.success ? '#4CAF50' : '#f44336'}`,
                  borderRadius: '8px',
                  backgroundColor: result.success ? '#f8fff8' : '#fff8f8'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                  <span style={{
                    display: 'inline-block',
                    padding: '4px 8px',
                    backgroundColor: result.success ? '#4CAF50' : '#f44336',
                    color: 'white',
                    borderRadius: '4px',
                    fontSize: '12px',
                    marginRight: '10px'
                  }}>
                    {result.type}
                  </span>
                  <span style={{
                    fontWeight: 'bold',
                    color: result.success ? '#2E7D32' : '#C62828'
                  }}>
                    {result.success ? '✅ 成功' : '❌ 失败'}
                  </span>
                </div>
                
                <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>
                  {result.description}
                </h4>
                
                {result.success && result.data && (
                  <div>
                    <strong>响应数据:</strong>
                    <pre style={{
                      backgroundColor: '#f5f5f5',
                      padding: '10px',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '12px',
                      marginTop: '5px'
                    }}>
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
                
                {!result.success && result.error && (
                  <div>
                    <strong>错误信息:</strong>
                    <div style={{
                      backgroundColor: '#ffebee',
                      padding: '10px',
                      borderRadius: '4px',
                      color: '#c62828',
                      marginTop: '5px',
                      fontSize: '14px'
                    }}>
                      {result.error}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 说明文档 */}
      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
        <h3>功能说明</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>空字符串加密</strong>: 测试传入空字符串 '' 时的加密处理</li>
          <li><strong>null数据加密</strong>: 测试传入 null 值时的加密处理</li>
          <li><strong>undefined数据加密</strong>: 测试传入 undefined 值时的加密处理</li>
          <li><strong>空对象加密</strong>: 测试传入空对象 {} 时的加密处理</li>
          <li><strong>GET请求加密</strong>: 测试GET请求（通常无请求体）时的加密处理</li>
          <li><strong>DELETE请求加密</strong>: 测试DELETE请求（通常无请求体）时的加密处理</li>
        </ul>
        
        <h4>预期行为</h4>
        <p>
          根据实现的逻辑，即使API没有请求参数值，也会走完整的加密流程：
        </p>
        <ol style={{ lineHeight: '1.6' }}>
          <li>将空数据（''、null、undefined等）转换为JSON字符串</li>
          <li>对JSON字符串进行Base64编码</li>
          <li>使用SM2算法加密Base64字符串</li>
          <li>生成包含设备信息和签名的完整请求参数</li>
        </ol>
      </div>
    </div>
  );
}
