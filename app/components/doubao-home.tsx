import { useEffect, useState } from "react";
import styles from "./doubao-home.module.scss";
import { IconButton } from "./button";
import { UserAvatar } from "./user-avatar";
import { useMobileScreen } from "../utils";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import { useAppConfig, useChatStore } from "../store";
import { isAuthenticated } from "../utils/auth-token";
import { showToast } from "./ui-lib";
import clsx from "clsx";

// Icons
import MenuIcon from "../icons/menu.svg";
import SettingsIcon from "../icons/settings.svg";
import UserIcon from "../icons/user.svg";
import DownloadIcon from "../icons/download.svg";
import LogoutIcon from "../icons/power.svg";
import InfoIcon from "../icons/brain.svg";
import CloseIcon from "../icons/close.svg";

interface DoubaoHomeProps {
  onToggleSidebar?: () => void;
  sidebarVisible?: boolean;
}

export function DoubaoHome({
  onToggleSidebar,
  sidebarVisible,
}: DoubaoHomeProps) {
  const navigate = useNavigate();
  const isMobile = useMobileScreen();
  const config = useAppConfig();
  const chatStore = useChatStore();
  const [userLoggedIn, setUserLoggedIn] = useState(isAuthenticated());

  const [showAboutMenu, setShowAboutMenu] = useState(false);
  const [showDownloadMenu, setShowDownloadMenu] = useState(false);

  useEffect(() => {
    setUserLoggedIn(isAuthenticated());
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 6) return "夜深了";
    if (hour < 12) return "早上好";
    if (hour < 18) return "下午好";
    return "晚上好";
  };

  const handleNewChat = () => {
    if (config.dontShowMaskSplashScreen) {
      chatStore.newSession();
      navigate(Path.Chat);
    } else {
      navigate(Path.NewChat);
    }
  };

  const handleLogin = () => {
    navigate(Path.Login);
  };

  const quickActions = [
    { name: "图像编辑", icon: "🎬", color: "#10B981", path: Path.ImageEdit },
    {
      name: "万物替换",
      icon: "🎨",
      color: "#8B5CF6",
      path: Path.ObjectReplace,
    },
    {
      name: "证件照生成",
      icon: "🖼️",
      color: "#3B82F6",
      path: Path.IdPhotoGenerate,
    },
    {
      name: "海报生成",
      icon: "🖼️",
      color: "#EF4444",
      path: Path.PosterGenerate,
    },
  ];

  const handleQuickAction = (actionPath: string) => {
    // 检查用户是否已登录
    if (!userLoggedIn) {
      navigate(Path.Login);
      return;
    }

    // 已登录，跳转到相应页面
    navigate(actionPath);
  };

  return (
    <div
      className={clsx(styles.container, {
        [styles.containerWithSidebar]: sidebarVisible,
      })}
    >
      {/* 顶部导航栏 */}
      <header className={styles.header}>
        <div className={styles.headerLeft}>
          {!sidebarVisible && (
            <IconButton
              icon={<MenuIcon />}
              onClick={onToggleSidebar}
              className={styles.menuButton}
            />
          )}
        </div>

        <div className={styles.headerRight}>
          <div className={styles.headerActions}>
            <button
              className={styles.downloadButton}
              onClick={() => setShowDownloadMenu(true)}
            >
              下载移动端
            </button>
            {userLoggedIn ? (
              <div className={styles.userSection}>
                <UserAvatar />
              </div>
            ) : (
              <button className={styles.loginButton} onClick={handleLogin}>
                登录
              </button>
            )}
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className={styles.main}>
        <div className={styles.welcomeSection}>
          <h1 className={styles.title}>
            {getGreeting()}，有什么我能帮你的吗？
          </h1>
          <p className={styles.subtitle}>创想AI，开启你的创意世界</p>
        </div>

        {/* 搜索输入框 */}
        <div className={styles.searchSection}>
          <div className={styles.searchBox} onClick={handleNewChat}>
            <span className={styles.searchPlaceholder}>发送消息或选择技能</span>
            <div className={styles.searchActions}>
              <IconButton
                icon={
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M7 11L12 6L17 11M12 18V7"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                }
                className={styles.sendButton}
              />
            </div>
          </div>
        </div>

        {/* 快捷操作 */}
        <div className={styles.quickActions}>
          {quickActions.map((action, index) => (
            <div
              key={index}
              className={styles.actionCard}
              onClick={() => handleQuickAction(action.path)}
              style={{ borderLeftColor: action.color }}
            >
              <span className={styles.actionIcon}>{action.icon}</span>
              <span className={styles.actionName}>{action.name}</span>
            </div>
          ))}
        </div>
      </main>

      {/* 侧边栏关于豆包气泡框 */}
      {showAboutMenu && (
        <div className={styles.aboutMenu}>
          <div className={styles.aboutContent}>
            <div className={styles.aboutHeader}>
              <h3>关于创想AI</h3>
              <IconButton
                icon={<CloseIcon />}
                onClick={() => setShowAboutMenu(false)}
                className={styles.closeButton}
              />
            </div>
            <div className={styles.aboutBody}>
              <div className={styles.aboutItem}>
                <a
                  href="https://app.51creativeai.com/app/creativeAIPrivacy"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  隐私政策
                </a>
              </div>
              <div className={styles.aboutItem}>
                <a
                  href="https://app.51creativeai.com/app/creativeAIUserService"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  服务协议
                </a>
              </div>
              <div className={styles.aboutItem}>
                <span>联系我们: <EMAIL></span>
              </div>
            </div>
            <div className={styles.aboutFooter}>
              <p>© 2025 创想AI. 版权所有。</p>
            </div>
          </div>
        </div>
      )}

      {/* 下载移动端气泡框 */}
      {showDownloadMenu && (
        <div
          className={clsx(styles.downloadMenu, {
            [styles.show]: showDownloadMenu,
          })}
        >
          <div className={styles.downloadContent}>
            <div className={styles.downloadHeader}>
              <h3>下载移动端</h3>
              <IconButton
                icon={<CloseIcon />}
                onClick={() => setShowDownloadMenu(false)}
                className={styles.closeButton}
              />
            </div>
            <div className={styles.downloadBody}>
              <div
                className={styles.downloadItem}
                onClick={() =>
                  window.open(
                    "https://play.google.com/store/apps/details?id=com.stcode.createai",
                    "_blank",
                  )
                }
              >
                <span>
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                  </svg>
                  Google Play
                </span>
              </div>
              <div
                className={styles.downloadItem}
                onClick={() => showToast("正在快马加鞭开发中，敬请期待~❤️~")}
              >
                <span>
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M18.71,19.5C17.88,20.74 17,21.95 15.66,21.97C14.32,22 13.89,21.18 12.37,21.18C10.84,21.18 10.37,21.95 9.1,22C7.79,22.05 6.8,20.68 5.96,19.47C4.25,17 2.94,12.45 4.7,9.39C5.57,7.87 7.13,6.91 8.82,6.88C10.1,6.86 11.32,7.75 12.11,7.75C12.89,7.75 14.37,6.68 15.92,6.84C16.57,6.87 18.39,7.1 19.56,8.82C19.47,8.88 17.39,10.1 17.41,12.63C17.44,15.65 20.06,16.66 20.09,16.67C20.06,16.74 19.67,18.11 18.71,19.5M13,3.5C13.73,2.67 14.94,2.04 15.94,2C16.07,3.17 15.6,4.35 14.9,5.19C14.21,6.04 13.07,6.7 11.95,6.61C11.8,5.46 12.36,4.26 13,3.5Z" />
                  </svg>
                  App Store
                </span>
              </div>
              <div className={styles.developmentNote}>
                <span>App Store 版本正在开发中</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 点击遮罩关闭菜单 */}
      {(showAboutMenu || showDownloadMenu) && (
        <div
          className={clsx(styles.overlay, {
            [styles.show]: showAboutMenu || showDownloadMenu,
          })}
          onClick={() => {
            setShowAboutMenu(false);
            setShowDownloadMenu(false);
          }}
        />
      )}
    </div>
  );
}

// 导出给侧边栏使用的关于豆包组件
export function AboutDoubao() {
  const [showAboutMenu, setShowAboutMenu] = useState(false);

  return (
    <>
      <div
        className={styles.aboutTrigger}
        onClick={() => setShowAboutMenu(true)}
      >
        关于创想AI
      </div>

      {showAboutMenu && (
        <>
          <div className={styles.aboutMenu}>
            <div className={styles.aboutContent}>
              <div className={styles.aboutHeader}>
                <h3>关于创想AI</h3>
                <IconButton
                  icon={<CloseIcon />}
                  onClick={() => setShowAboutMenu(false)}
                  className={styles.closeButton}
                />
              </div>
              <div className={styles.aboutBody}>
                <div className={styles.aboutItem}>
                  <a
                    href="https://app.51creativeai.com/app/creativeAIPrivacy"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    隐私政策
                  </a>
                </div>
                <div className={styles.aboutItem}>
                  <a
                    href="https://app.51creativeai.com/app/creativeAIUserService"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    服务协议
                  </a>
                </div>
                <div className={styles.aboutItem}>
                  <span>联系我们: <EMAIL></span>
                </div>
              </div>
              <div className={styles.aboutFooter}>
                <p>© 2025 创想AI. 版权所有。</p>
              </div>
            </div>
          </div>
          <div
            className={clsx(styles.overlay, { [styles.show]: showAboutMenu })}
            onClick={() => setShowAboutMenu(false)}
          />
        </>
      )}
    </>
  );
}
