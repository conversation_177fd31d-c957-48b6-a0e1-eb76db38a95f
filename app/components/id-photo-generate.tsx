import React from "react";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import { IconButton } from "./button";
import styles from "./image-edit.module.scss";

// Icons
import BackIcon from "../icons/back.svg";

export function IdPhotoGenerate() {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(Path.Home);
  };

  return (
    <div className={styles.container}>
      {/* 顶部导航栏 */}
      <div className={styles.header}>
        <IconButton
          icon={<BackIcon />}
          text="返回"
          className={styles.backButton}
          onClick={handleBack}
        />
        <h1 className={styles.title}>证件照处理</h1>
      </div>

      {/* 主要内容区域 */}
      <div className={styles.mainContent}>
        <div className={styles.comingSoon}>
          <h2>功能开发中</h2>
          <p>证件照处理功能正在开发中，敬请期待！</p>
        </div>
      </div>
    </div>
  );
}