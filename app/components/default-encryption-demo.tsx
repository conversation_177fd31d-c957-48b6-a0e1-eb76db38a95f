/**
 * 默认加密演示组件
 * 展示API默认加密和登录接口不加密的功能
 */

"use client";

import React, { useState } from 'react';
import { customApi, authApi, userApi, chatApi } from '../api/custom-api';

interface DemoState {
  loading: boolean;
  results: Array<{
    api: string;
    method: string;
    encrypted: boolean;
    description: string;
    success: boolean;
    data?: any;
    error?: string;
  }>;
}

export function DefaultEncryptionDemo() {
  const [state, setState] = useState<DemoState>({
    loading: false,
    results: []
  });

  const addResult = (result: DemoState['results'][0]) => {
    setState(prev => ({
      ...prev,
      results: [...prev.results, result]
    }));
  };

  const clearResults = () => {
    setState(prev => ({
      ...prev,
      results: []
    }));
  };

  // 测试登录接口（不加密）
  const testLogin = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await authApi.login({
        username: '<EMAIL>',
        password: 'demo123456'
      });
      
      addResult({
        api: '/auth/login',
        method: 'POST',
        encrypted: false,
        description: '用户登录（不加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/auth/login',
        method: 'POST',
        encrypted: false,
        description: '用户登录（不加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  // 测试注册接口（不加密）
  const testRegister = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await authApi.register({
        username: 'newuser',
        password: 'password123',
        email: '<EMAIL>',
        captcha: '1234'
      });
      
      addResult({
        api: '/auth/register',
        method: 'POST',
        encrypted: false,
        description: '用户注册（不加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/auth/register',
        method: 'POST',
        encrypted: false,
        description: '用户注册（不加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  // 测试获取用户信息（默认加密）
  const testGetUserInfo = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await authApi.getUserInfo();
      
      addResult({
        api: '/user/info',
        method: 'GET',
        encrypted: true,
        description: '获取用户信息（默认加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/user/info',
        method: 'GET',
        encrypted: true,
        description: '获取用户信息（默认加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  // 测试更新用户资料（默认加密）
  const testUpdateProfile = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await userApi.updateProfile({
        nickname: '新昵称',
        bio: '这是一个测试用户'
      });
      
      addResult({
        api: '/user/profile',
        method: 'POST',
        encrypted: true,
        description: '更新用户资料（默认加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/user/profile',
        method: 'POST',
        encrypted: true,
        description: '更新用户资料（默认加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  // 测试创建聊天会话（默认加密）
  const testCreateSession = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await chatApi.createSession({
        title: '测试对话',
        model: 'gpt-4',
        systemPrompt: '你是一个有用的助手'
      });
      
      addResult({
        api: '/chat/session',
        method: 'POST',
        encrypted: true,
        description: '创建聊天会话（默认加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/chat/session',
        method: 'POST',
        encrypted: true,
        description: '创建聊天会话（默认加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  // 测试强制不加密的API调用
  const testPlainApi = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await customApi.post('/public/test', {
        message: '这是一个公开的API调用'
      }, {
        withEncryption: false
      });
      
      addResult({
        api: '/public/test',
        method: 'POST',
        encrypted: false,
        description: '公开API（强制不加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/public/test',
        method: 'POST',
        encrypted: false,
        description: '公开API（强制不加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  // 测试默认加密的API调用
  const testDefaultEncryptedApi = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await customApi.post('/secure/test', {
        message: '这是一个需要加密的API调用',
        timestamp: Date.now()
      });
      
      addResult({
        api: '/secure/test',
        method: 'POST',
        encrypted: true,
        description: '安全API（默认加密）',
        success: true,
        data: response
      });
    } catch (error) {
      addResult({
        api: '/secure/test',
        method: 'POST',
        encrypted: true,
        description: '安全API（默认加密）',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
    }
    
    setState(prev => ({ ...prev, loading: false }));
  };

  const runAllTests = async () => {
    clearResults();
    const tests = [
      testLogin,
      testRegister,
      testGetUserInfo,
      testUpdateProfile,
      testCreateSession,
      testPlainApi,
      testDefaultEncryptedApi
    ];

    for (const test of tests) {
      await test();
      await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>默认加密功能演示</h1>
      <p>展示API默认加密和特定接口不加密的功能</p>
      
      {/* 控制按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          <button onClick={testLogin} disabled={state.loading}>
            测试登录（不加密）
          </button>
          <button onClick={testRegister} disabled={state.loading}>
            测试注册（不加密）
          </button>
          <button onClick={testGetUserInfo} disabled={state.loading}>
            测试获取用户信息（加密）
          </button>
          <button onClick={testUpdateProfile} disabled={state.loading}>
            测试更新资料（加密）
          </button>
        </div>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', marginBottom: '10px' }}>
          <button onClick={testCreateSession} disabled={state.loading}>
            测试创建会话（加密）
          </button>
          <button onClick={testPlainApi} disabled={state.loading}>
            测试公开API（不加密）
          </button>
          <button onClick={testDefaultEncryptedApi} disabled={state.loading}>
            测试安全API（加密）
          </button>
        </div>
        <div style={{ display: 'flex', gap: '10px' }}>
          <button onClick={runAllTests} disabled={state.loading}>
            运行所有测试
          </button>
          <button onClick={clearResults} disabled={state.loading}>
            清除结果
          </button>
        </div>
      </div>

      {/* 加载状态 */}
      {state.loading && (
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#f0f0f0', 
          borderRadius: '5px', 
          marginBottom: '20px',
          textAlign: 'center'
        }}>
          正在测试API调用...
        </div>
      )}

      {/* 测试结果 */}
      {state.results.length > 0 && (
        <div>
          <h3>测试结果</h3>
          <div style={{ display: 'grid', gap: '15px' }}>
            {state.results.map((result, index) => (
              <div
                key={index}
                style={{
                  padding: '15px',
                  border: `2px solid ${result.success ? '#4CAF50' : '#f44336'}`,
                  borderRadius: '8px',
                  backgroundColor: result.success ? '#f8fff8' : '#fff8f8'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                  <span style={{
                    display: 'inline-block',
                    padding: '4px 8px',
                    backgroundColor: result.method === 'GET' ? '#2196F3' : '#FF9800',
                    color: 'white',
                    borderRadius: '4px',
                    fontSize: '12px',
                    marginRight: '10px'
                  }}>
                    {result.method}
                  </span>
                  <span style={{
                    display: 'inline-block',
                    padding: '4px 8px',
                    backgroundColor: result.encrypted ? '#9C27B0' : '#607D8B',
                    color: 'white',
                    borderRadius: '4px',
                    fontSize: '12px',
                    marginRight: '10px'
                  }}>
                    {result.encrypted ? '🔒 加密' : '🔓 不加密'}
                  </span>
                  <span style={{
                    fontWeight: 'bold',
                    color: result.success ? '#2E7D32' : '#C62828'
                  }}>
                    {result.success ? '✅ 成功' : '❌ 失败'}
                  </span>
                </div>
                
                <h4 style={{ margin: '0 0 5px 0', color: '#333' }}>
                  {result.api}
                </h4>
                <p style={{ margin: '0 0 10px 0', color: '#666', fontSize: '14px' }}>
                  {result.description}
                </p>
                
                {result.success && result.data && (
                  <div>
                    <strong>响应数据:</strong>
                    <pre style={{
                      backgroundColor: '#f5f5f5',
                      padding: '10px',
                      borderRadius: '4px',
                      overflow: 'auto',
                      fontSize: '12px',
                      marginTop: '5px',
                      maxHeight: '200px'
                    }}>
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                )}
                
                {!result.success && result.error && (
                  <div>
                    <strong>错误信息:</strong>
                    <div style={{
                      backgroundColor: '#ffebee',
                      padding: '10px',
                      borderRadius: '4px',
                      color: '#c62828',
                      marginTop: '5px',
                      fontSize: '14px'
                    }}>
                      {result.error}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 说明文档 */}
      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f9f9f9', borderRadius: '8px' }}>
        <h3>功能说明</h3>
        
        <h4>🔒 默认加密</h4>
        <p>现在所有API请求默认启用加密，除非明确设置 <code>withEncryption: false</code></p>
        
        <h4>🔓 不加密的接口</h4>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>登录接口</strong>: <code>/auth/login</code> - 用户登录</li>
          <li><strong>注册接口</strong>: <code>/auth/register</code> - 用户注册</li>
          <li><strong>登出接口</strong>: <code>/auth/logout</code> - 用户登出</li>
          <li><strong>刷新Token</strong>: <code>/auth/refresh</code> - 刷新访问令牌</li>
          <li><strong>发送验证码</strong>: <code>/auth/send-captcha</code> - 发送验证码</li>
          <li><strong>文件上传</strong>: <code>/user/avatar</code> - 头像上传</li>
        </ul>
        
        <h4>🔒 默认加密的接口</h4>
        <ul style={{ lineHeight: '1.6' }}>
          <li><strong>用户信息</strong>: <code>/user/info</code> - 获取用户信息</li>
          <li><strong>用户资料</strong>: <code>/user/profile</code> - 更新用户资料</li>
          <li><strong>聊天会话</strong>: <code>/chat/session</code> - 创建/管理聊天会话</li>
          <li><strong>聊天消息</strong>: <code>/chat/message</code> - 发送聊天消息</li>
          <li><strong>其他业务API</strong>: 除明确设置不加密外的所有接口</li>
        </ul>
        
        <h4>⚙️ 手动控制</h4>
        <p>可以通过配置参数手动控制加密行为：</p>
        <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', fontSize: '14px' }}>
{`// 强制不加密
await customApi.post('/api/endpoint', data, { withEncryption: false });

// 强制加密（默认行为）
await customApi.post('/api/endpoint', data, { withEncryption: true });

// 使用默认设置（加密）
await customApi.post('/api/endpoint', data);`}
        </pre>
      </div>
    </div>
  );
}
