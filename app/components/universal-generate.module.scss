.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--white);
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--white);
  position: relative;
  z-index: 10;
}

.backButton {
  margin-right: 16px;
  
  svg {
    width: 20px;
    height: 20px;
  }
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin: 0;
}

.content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧面板 */
.leftPanel {
  width: 400px;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  background: var(--white);
}

.tabNav {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background: var(--white);
}

.tab {
  flex: 1;
  padding: 16px 24px;
  border: none;
  background: transparent;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-secondary);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    color: var(--text-color);
    background: var(--hover-color);
  }

  &.active {
    color: var(--primary);
    background: var(--white);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
  }

  &.active::after {
    width: 60%;
  }
}

.instantTab,
.resultsTab {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.usageTip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #e0f2fe;
  border: 1px solid #b3e5fc;
  border-radius: 8px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #0277bd;
}

.tipIcon {
  font-size: 16px;
}

.examplesSection {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 16px 0;
}

.exampleGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.exampleCard {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  img {
    width: 100%;
    height: 120px;
    object-fit: cover;
  }
}

.exampleInfo {
  padding: 12px;

  h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 4px 0;
  }

  p {
    font-size: 12px;
    color: var(--text-color-secondary);
    margin: 0;
    line-height: 1.4;
  }
}

.emptyResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: var(--text-color-secondary);

  svg {
    width: 48px;
    height: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
  }

  p {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
  }

  span {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

.resultGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.resultCard {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  img {
    width: 100%;
    height: 150px;
    object-fit: cover;
  }
}

.resultInfo {
  padding: 12px;
}

.resultPrompt {
  font-size: 13px;
  color: var(--black);
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.resultTime {
  font-size: 12px;
  color: var(--text-color-secondary);
}

/* 右侧面板 */
.rightPanel {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: var(--white);
}

.uploadSection {
  margin-bottom: 24px;
}

.uploadLabel {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 8px;
}

.required {
  color: #ef4444;
}

.uploadArea {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: var(--primary);
    background: var(--hover-color);
  }
}

.uploadPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.uploadIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;

  svg {
    width: 48px;
    height: 48px;
    color: #d1d5db;
    transition: all 0.3s ease;
  }

  &:hover {
    svg {
      color: #9ca3af;
      transform: scale(1.05);
    }
  }
}

.uploadText {
  font-size: 14px;
  color: #9ca3af;
  font-weight: 400;
  line-height: 1.4;
  margin: 0;
}

.uploadedImage {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  border-radius: 4px;
}

.promptSection {
  margin-bottom: 24px;
}

.promptLabel {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 8px;
}

.promptInput {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: var(--text-color-secondary);
  }
}

.generateSection {
  text-align: center;
}

.generateButton {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.usageInfo {
  text-align: center;
  font-size: 12px;
  color: var(--text-color-secondary);
  line-height: 1.5;
}

.freeUsage {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
  color: #f59e0b;

  svg {
    width: 14px;
    height: 14px;
  }
}

.costInfo {
  margin-bottom: 4px;
}

.apiInfo {
  color: var(--primary);
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    flex-direction: column;
  }

  .leftPanel {
    width: 100%;
    height: 50vh;
  }

  .rightPanel {
    padding: 16px;
  }

  .exampleGrid,
  .resultGrid {
    grid-template-columns: 1fr;
  }
}
