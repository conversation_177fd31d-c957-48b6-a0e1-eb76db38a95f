"use client";

import { useEffect, useState } from "react";
import styles from "./home.module.scss";

// Async polyfill loading
if (typeof window !== "undefined") {
  import("../utils/polyfill-loader").catch(() => {});
}

import BotIcon from "../icons/llm-icons/deepseek.svg";
import LoadingIcon from "../icons/deepseek-loading.svg";
import MenuIcon from "../icons/menu.svg";

import { getCSSVar, useMobileScreen } from "../utils";

import dynamic from "next/dynamic";
import { Path, SlotID } from "../constant";
import { ErrorBoundary } from "./error";

import { getISOLang, getLang } from "../locales";

import {
  HashRouter as Router,
  Route,
  Routes,
  useLocation,
} from "react-router-dom";
import { SideBar } from "./sidebar";
import { DoubaoHome } from "./doubao-home";
import { useAppConfig } from "../store/config";
import { AuthPage } from "./auth";
import { getClientConfig } from "../config/client";
import { type ClientApi, getClientApi } from "../client/api";
import { useAccessStore } from "../store";
import clsx from "clsx";
import { initializeMcpSystem, isMcpEnabled } from "../mcp/actions";

export function Loading(props: { noLogo?: boolean }) {
  return (
    <div className={clsx("no-dark", styles["loading-content"])}>
      {!props.noLogo && <BotIcon />}
      <div className={styles["loading-spinner"]}>
        <LoadingIcon />
      </div>
    </div>
  );
}

const Artifacts = dynamic(async () => (await import("./artifacts")).Artifacts, {
  loading: () => <Loading noLogo />,
});

const Settings = dynamic(async () => (await import("./settings")).Settings, {
  loading: () => <Loading noLogo />,
});

const Chat = dynamic(async () => (await import("./chat")).Chat, {
  loading: () => <Loading noLogo />,
});

const NewChat = dynamic(async () => (await import("./new-chat")).NewChat, {
  loading: () => <Loading noLogo />,
});

const MaskPage = dynamic(async () => (await import("./mask")).MaskPage, {
  loading: () => <Loading noLogo />,
});

const PluginPage = dynamic(async () => (await import("./plugin")).PluginPage, {
  loading: () => <Loading noLogo />,
});

const SearchChat = dynamic(
  async () => (await import("./search-chat")).SearchChatPage,
  {
    loading: () => <Loading noLogo />,
  },
);

const Sd = dynamic(async () => (await import("./sd")).Sd, {
  loading: () => <Loading noLogo />,
});

const McpMarketPage = dynamic(
  async () => (await import("./mcp-market")).McpMarketPage,
  {
    loading: () => <Loading noLogo />,
  },
);

const LoginPage = dynamic(async () => (await import("./login")).LoginPage, {
  loading: () => <Loading noLogo />,
});

const RegisterPage = dynamic(
  async () => (await import("./register")).RegisterPage,
  {
    loading: () => <Loading noLogo />,
  },
);

const RechargePage = dynamic(
  async () => (await import("./recharge")).RechargePage,
  {
    loading: () => <Loading noLogo />,
  },
);

const RechargeHistoryPage = dynamic(
  async () => (await import("./recharge-history")).RechargeHistoryPage,
  {
    loading: () => <Loading noLogo />,
  },
);

const MyAccountPage = dynamic(
  async () => (await import("./my-account")).MyAccountPage,
  {
    loading: () => <Loading noLogo />,
  },
);

export function useSwitchTheme() {
  const config = useAppConfig();

  useEffect(() => {
    document.body.classList.remove("light");
    document.body.classList.remove("dark");

    if (config.theme === "dark") {
      document.body.classList.add("dark");
    } else if (config.theme === "light") {
      document.body.classList.add("light");
    }

    const metaDescriptionDark = document.querySelector(
      'meta[name="theme-color"][media*="dark"]',
    );
    const metaDescriptionLight = document.querySelector(
      'meta[name="theme-color"][media*="light"]',
    );

    if (config.theme === "auto") {
      metaDescriptionDark?.setAttribute("content", "#151515");
      metaDescriptionLight?.setAttribute("content", "#fafafa");
    } else {
      const themeColor = getCSSVar("--theme-color");
      metaDescriptionDark?.setAttribute("content", themeColor);
      metaDescriptionLight?.setAttribute("content", themeColor);
    }
  }, [config.theme]);
}

function useHtmlLang() {
  useEffect(() => {
    const lang = getISOLang();
    const htmlLang = document.documentElement.lang;

    if (lang !== htmlLang) {
      document.documentElement.lang = lang;
    }
  }, []);
}

const useHasHydrated = () => {
  const [hasHydrated, setHasHydrated] = useState<boolean>(false);

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  return hasHydrated;
};

const loadAsyncGoogleFont = () => {
  if (typeof document !== "undefined") {
    const linkEl = document.createElement("link");
    const proxyFontUrl = "/google-fonts";
    const remoteFontUrl = "https://fonts.googleapis.com";
    const googleFontUrl =
      getClientConfig()?.buildMode === "export" ? remoteFontUrl : proxyFontUrl;
    linkEl.rel = "stylesheet";
    linkEl.href =
      googleFontUrl +
      "/css2?family=" +
      encodeURIComponent("Noto Sans:wght@300;400;700;900") +
      "&display=swap";
    linkEl.media = "print";
    linkEl.onload = () => {
      linkEl.media = "all";
    };
    document.head.appendChild(linkEl);
  }
};

export function WindowContent(props: {
  children: React.ReactNode;
  sidebarVisible?: boolean;
}) {
  return (
    <div
      className={clsx(styles["window-content"], {
        [styles["window-content-with-sidebar"]]: props.sidebarVisible,
      })}
      id={SlotID.AppBody}
    >
      {props?.children}
    </div>
  );
}

function Screen() {
  const config = useAppConfig();
  const location = useLocation();
  const isArtifact = location.pathname.includes(Path.Artifacts);
  const isHome = location.pathname === Path.Home;
  console.log(
    "[Debug] Current pathname:",
    location.pathname,
    "Path.Home:",
    Path.Home,
    "isHome:",
    isHome,
  );
  const isAuth = location.pathname === Path.Auth;
  const isLogin = location.pathname === Path.Login;
  const isRegister = location.pathname === Path.Register;
  const isRecharge = location.pathname === Path.Recharge;
  const isRechargeHistory = location.pathname === Path.RechargeHistory;
  const isSd = location.pathname === Path.Sd;
  const isSdNew = location.pathname === Path.SdNew;
  const isNewChat = location.pathname === Path.NewChat;

  const isMobileScreen = useMobileScreen();
  const shouldTightBorder =
    getClientConfig()?.isApp || (config.tightBorder && !isMobileScreen);

  const [sidebarVisible, setSidebarVisible] = useState(true);

  useEffect(() => {
    loadAsyncGoogleFont();
  }, []);

  if (isArtifact) {
    return (
      <Routes>
        <Route path="/artifacts/:id" element={<Artifacts />} />
      </Routes>
    );
  }

  const handleToggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  const renderContent = () => {
    if (isAuth) return <AuthPage />;
    if (isLogin) return <LoginPage />;
    if (isRegister) return <RegisterPage />;
    if (isRecharge) return <RechargePage />;
    if (isRechargeHistory) return <RechargeHistoryPage />;
    if (isSd) return <Sd />;
    if (location.pathname === Path.MyAccount) return <MyAccountPage />;
    if (isSdNew) return <Sd />;

    // 如果是首页，显示豆包风格的首页
    if (isHome) {
      return (
        <>
          <SideBar
            className={clsx({
              [styles["sidebar-show"]]: sidebarVisible,
            })}
            onToggleSidebar={handleToggleSidebar}
          />
          <DoubaoHome
            onToggleSidebar={handleToggleSidebar}
            sidebarVisible={sidebarVisible}
          />
        </>
      );
    }

    return (
      <>
        <SideBar
          className={clsx({
            [styles["sidebar-show"]]: sidebarVisible,
          })}
          onToggleSidebar={handleToggleSidebar}
        />

        <WindowContent sidebarVisible={sidebarVisible}>
          <Routes>
            <Route path={Path.NewChat} element={<NewChat />} />
            <Route path={Path.Masks} element={<MaskPage />} />
            <Route path={Path.Plugins} element={<PluginPage />} />
            <Route path={Path.SearchChat} element={<SearchChat />} />
            <Route
              path={Path.Chat}
              element={
                <Chat
                  onToggleSidebar={handleToggleSidebar}
                  sidebarVisible={sidebarVisible}
                />
              }
            />
            <Route path={Path.Settings} element={<Settings />} />
            <Route path={Path.Recharge} element={<RechargePage />} />
            <Route
              path={Path.RechargeHistory}
              element={<RechargeHistoryPage />}
            />
            <Route path={Path.McpMarket} element={<McpMarketPage />} />
          </Routes>
        </WindowContent>
      </>
    );
  };

  return (
    <div
      className={clsx(styles.container, {
        [styles["tight-container"]]: shouldTightBorder,
        [styles["rtl-screen"]]: getLang() === "ar",
      })}
    >
      <div className={styles["main-wrapper"]}>{renderContent()}</div>
      {!isHome && isMobileScreen && (
        <div className={styles.footer}>
          <div className={styles["footer-links"]}>
            <a
              href="https://app.51creativeai.com/app/creativeAIPrivacy"
              target="_blank"
              rel="noopener noreferrer"
              className={styles["footer-link"]}
            >
              隐私政策
            </a>
            <span className={styles["footer-divider"]}>|</span>
            <a
              href="https://app.51creativeai.com/app/creativeAIUserService"
              target="_blank"
              rel="noopener noreferrer"
              className={styles["footer-link"]}
            >
              服务协议
            </a>
            <span className={styles["footer-divider"]}>|</span>
            <span className={styles["footer-contact"]}>
              联系我们: <EMAIL>
            </span>
          </div>
          <div className={styles["footer-copyright"]}>
            © 2025 创想AI. 版权所有。
          </div>
        </div>
      )}
    </div>
  );
}

export function useLoadData() {
  const config = useAppConfig();

  const api: ClientApi = getClientApi(config.modelConfig.providerName);

  useEffect(() => {
    (async () => {
      const models = await api.llm.models();
      config.mergeModels(models);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}

export function Home() {
  useSwitchTheme();
  useLoadData();
  useHtmlLang();

  useEffect(() => {
    console.log("[Config] got config from build time", getClientConfig());
    useAccessStore.getState().fetch();

    const initMcp = async () => {
      try {
        const enabled = await isMcpEnabled();
        if (enabled) {
          console.log("[MCP] initializing...");
          await initializeMcpSystem();
          console.log("[MCP] initialized");
        }
      } catch (err) {
        console.error("[MCP] failed to initialize:", err);
      }
    };
    initMcp();
  }, []);

  if (!useHasHydrated()) {
    return <Loading />;
  }

  return (
    <ErrorBoundary>
      <Router>
        <Screen />
      </Router>
    </ErrorBoundary>
  );
}
