import React, { useState, useRef, useCallback, useEffect } from "react";
import { IconButton } from "./button";
import { useNavigate } from "react-router-dom";
import styles from "./image-edit.module.scss";

import UploadIcon from "../icons/upload.svg";
import BackIcon from "../icons/back.svg";
import CloseIcon from "../icons/close.svg";

// 类型定义
type ImageType = 'source' | 'target';
type TabType = 'immediate' | 'result';
type ToolType = 'brush' | 'eraser';

interface Point {
  x: number;
  y: number;
}

interface Example {
  id: number;
  beforeImage: string;
  afterImage: string;
  title: string;
}

interface Material {
  id: number;
  image: string;
  name: string;
  type: string;
}

interface Example {
  id: number;
  beforeImage: string;
  afterImage: string;
  title: string;
}

interface Material {
  id: number;
  image: string;
  name: string;
  type: string;
}

// 常量定义
const DEFAULT_BRUSH_SIZE = 32;
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const PROCESSING_TIMEOUT = 30000; // 30秒

// 案例配置
const EXAMPLES: Example[] = [
  {
    id: 1,
    beforeImage: "https://dummyimage.com/500x350/4F46E5/ffffff/png?text=Before+Image",
    afterImage: "https://dummyimage.com/500x350/10B981/ffffff/png?text=After+Image",
    title: "案例展示"
  }
];

// 素材配置
const MATERIALS: Material[] = [
  {
    id: 1,
    image: "https://dummyimage.com/120x80/E5E7EB/6B7280&text=Living",
    name: "客厅场景",
    type: "source"
  },
  {
    id: 2,
    image: "https://dummyimage.com/300x80/F3F4F6/6B7280&text=Office",
    name: "办公室场景",
    type: "source"
  },
  {
    id: 3,
    image: "https://dummyimage.com/120x80/DBEAFE/3B82F6&text=Outdoor",
    name: "户外场景",
    type: "target"
  },
  {
    id: 4,
    image: "https://dummyimage.com/120x80/FEF3C7/F59E0B&text=Store",
    name: "商店场景",
    type: "target"
  }
];

export function ImageEdit() {
  const navigate = useNavigate();

  // 主要状态
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [description, setDescription] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>("immediate");
  const [error, setError] = useState<string | null>(null);

  // 局部重绘相关状态
  const [showInpaintModal, setShowInpaintModal] = useState(false);
  const [currentEditingImage, setCurrentEditingImage] = useState<ImageType | null>(null);
  const [brushSize, setBrushSize] = useState(DEFAULT_BRUSH_SIZE);
  const [isDrawing, setIsDrawing] = useState(false);
  const [maskHistory, setMaskHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [currentTool, setCurrentTool] = useState<ToolType>('brush');
  const [lastPoint, setLastPoint] = useState<Point | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // 工具函数
  const validateFile = useCallback((file: File): string | null => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `不支持的文件类型。请选择 ${ALLOWED_FILE_TYPES.join(', ')} 格式的图片。`;
    }
    if (file.size > MAX_FILE_SIZE) {
      return `文件大小超过限制。请选择小于 ${MAX_FILE_SIZE / 1024 / 1024}MB 的图片。`;
    }
    return null;
  }, []);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const showError = useCallback((message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000); // 5秒后自动清除错误
  }, []);

  const handleImageUpload = useCallback((
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件
    const validationError = validateFile(file);
    if (validationError) {
      showError(validationError);
      return;
    }

    resetError();

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imageUrl = e.target?.result as string;
        if (!imageUrl) {
          throw new Error('无法读取图片文件');
        }

        setUploadedImage(imageUrl);

        // 关闭编辑器如果正在打开
        if (showInpaintModal) {
          setShowInpaintModal(false);
          setCurrentEditingImage(null);
        }

        // 重置绘制状态
        setIsDrawing(false);
        setLastPoint(null);
        setMaskHistory([]);
        setHistoryIndex(-1);
      } catch (err) {
        showError('图片加载失败，请重试');
        console.error('Image upload error:', err);
      }
    };

    reader.onerror = () => {
      showError('文件读取失败，请重试');
    };

    reader.readAsDataURL(file);
  }, [validateFile, showError, resetError, showInpaintModal]);



  // 删除图片
  const removeImage = useCallback(() => {
    try {
      resetError();
      setUploadedImage(null);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // 关闭编辑器如果正在打开
      if (showInpaintModal) {
        setShowInpaintModal(false);
        setCurrentEditingImage(null);
      }

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);
    } catch (err) {
      console.error('Remove image error:', err);
      showError('删除图片时出错');
    }
  }, [resetError, showError, showInpaintModal]);

  // 打开局部重绘弹框
  const handleOpenInpaint = useCallback(() => {
    try {
      resetError();

      if (!uploadedImage) {
        showError('请先上传图片');
        return;
      }

      // 重置所有绘制相关状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);
      setBrushSize(DEFAULT_BRUSH_SIZE);
      setCurrentTool('brush');

      setCurrentEditingImage('source');
      setShowInpaintModal(true);
    } catch (err) {
      console.error('Open inpaint modal error:', err);
      showError('打开编辑器失败');
    }
  }, [resetError, showError, uploadedImage]);

  // 关闭局部重绘弹框
  const handleCloseInpaint = useCallback(() => {
    try {
      setShowInpaintModal(false);
      setCurrentEditingImage(null);
      // 清理绘制状态
      setIsDrawing(false);
      setLastPoint(null);
    } catch (err) {
      console.error('Close inpaint modal error:', err);
    }
  }, []);

  // 初始化画布
  const initializeCanvas = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const image = imageRef.current;

      if (!canvas || !image) {
        console.warn('Canvas or image not available for initialization');
        return;
      }

      // 等待图片完全加载
      if (!image.complete || image.naturalWidth === 0) {
        console.warn('Image not fully loaded, retrying...');
        setTimeout(initializeCanvas, 100);
        return;
      }

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('Failed to get canvas context');
        showError('画布初始化失败');
        return;
      }

      // 获取图片尺寸
      const imageWidth = image.naturalWidth || image.width;
      const imageHeight = image.naturalHeight || image.height;

      if (imageWidth === 0 || imageHeight === 0) {
        console.error('Invalid image dimensions:', imageWidth, imageHeight);
        showError('图片尺寸无效');
        return;
      }

      // 设置画布尺寸与图片相同
      canvas.width = imageWidth;
      canvas.height = imageHeight;

      // 设置画布样式尺寸
      const rect = image.getBoundingClientRect();
      if (rect.width > 0 && rect.height > 0) {
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';
      }

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);

      // 保存初始状态到历史记录
      const initialState = canvas.toDataURL();
      setMaskHistory([initialState]);
      setHistoryIndex(0);

      // 重置绘制状态，确保状态一致性
      setIsDrawing(false);
      setLastPoint(null);

      console.log('Canvas initialized successfully:', canvas.width, 'x', canvas.height);
    } catch (err) {
      console.error('Canvas initialization error:', err);
      showError('画布初始化失败');
    }
  }, [showError]);

  // 开始绘制
  const startDrawing = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    setIsDrawing(true);
    setLastPoint({ x, y });

    // 设置画笔属性
    ctx.globalCompositeOperation = currentTool === 'brush' ? 'source-over' : 'destination-out';
    ctx.strokeStyle = currentTool === 'brush' ? 'rgba(59, 130, 246, 0.6)' : 'rgba(239, 68, 68, 0.8)';
    ctx.lineWidth = brushSize;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 开始新的路径，但不立即绘制
    ctx.beginPath();
    ctx.moveTo(x, y);
  }, [brushSize, currentTool]);

  // 绘制
  const draw = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !lastPoint) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    // 计算距离，避免绘制过于密集的点
    const distance = Math.sqrt(Math.pow(x - lastPoint.x, 2) + Math.pow(y - lastPoint.y, 2));
    if (distance < 2) return; // 如果移动距离太小，跳过绘制

    // 继续当前路径
    ctx.lineTo(x, y);
    ctx.stroke();

    // 更新最后一个点
    setLastPoint({ x, y });
  }, [isDrawing, lastPoint]);

  // 停止绘制
  const stopDrawing = useCallback(() => {
    if (!isDrawing) return;

    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx && lastPoint) {
        // 如果只有一个点（没有移动），绘制一个小圆点
        ctx.globalCompositeOperation = currentTool === 'brush' ? 'source-over' : 'destination-out';
        ctx.fillStyle = currentTool === 'brush' ? 'rgba(59, 130, 246, 0.6)' : 'rgba(239, 68, 68, 0.8)';
        ctx.beginPath();
        ctx.arc(lastPoint.x, lastPoint.y, brushSize / 2, 0, Math.PI * 2);
        ctx.fill();
      }
    }

    setIsDrawing(false);
    setLastPoint(null);

    // 保存当前状态到历史记录
    if (canvas) {
      const currentState = canvas.toDataURL();
      const newHistory = maskHistory.slice(0, historyIndex + 1);
      newHistory.push(currentState);
      setMaskHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  }, [isDrawing, lastPoint, brushSize, maskHistory, historyIndex, currentTool]);

  // 撤销
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);

      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = maskHistory[newIndex];
        }
      }
    }
  }, [historyIndex, maskHistory]);

  // 重做
  const handleRedo = useCallback(() => {
    if (historyIndex < maskHistory.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);

      const canvas = canvasRef.current;
      if (canvas) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          const img = new Image();
          img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
          };
          img.src = maskHistory[newIndex];
        }
      }
    }
  }, [historyIndex, maskHistory]);



  // 重置画布
  const handleReset = useCallback(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        const initialState = canvas.toDataURL();
        setMaskHistory([initialState]);
        setHistoryIndex(0);
      }
    }
  }, []);

  // 确认局部重绘
  const handleConfirmInpaint = useCallback(() => {
    try {
      const canvas = canvasRef.current;
      const image = imageRef.current;

      if (!canvas || !image || !currentEditingImage) {
        console.error('Canvas, image or currentEditingImage not found');
        showError('编辑器状态异常，请重新打开');
        return;
      }

      // 验证画布和图片尺寸
      if (canvas.width === 0 || canvas.height === 0) {
        console.error('Canvas has invalid dimensions');
        showError('画布尺寸异常，请重新打开编辑器');
        return;
      }

      // 创建一个新的canvas来合成最终图片
      const finalCanvas = document.createElement('canvas');
      const finalCtx = finalCanvas.getContext('2d');

      if (!finalCtx) {
        console.error('Failed to get canvas context');
        showError('无法创建画布上下文');
        return;
      }

      // 设置最终canvas的尺寸
      const imageWidth = image.naturalWidth || image.width;
      const imageHeight = image.naturalHeight || image.height;

      if (imageWidth === 0 || imageHeight === 0) {
        console.error('Image has invalid dimensions');
        showError('图片尺寸异常');
        return;
      }

      finalCanvas.width = imageWidth;
      finalCanvas.height = imageHeight;

      // 绘制原始图片
      finalCtx.drawImage(image, 0, 0);

      // 创建黑色遮罩版本
      const maskCanvas = document.createElement('canvas');
      const maskCtx = maskCanvas.getContext('2d');

      if (maskCtx) {
        maskCanvas.width = canvas.width;
        maskCanvas.height = canvas.height;

        // 绘制原始遮罩
        maskCtx.drawImage(canvas, 0, 0);

        // 将蓝色区域转换为黑色
        const imageData = maskCtx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          // 如果像素有透明度（即被涂抹过）
          if (data[i + 3] > 0) {
            data[i] = 0;     // R = 0 (黑色)
            data[i + 1] = 0; // G = 0 (黑色)
            data[i + 2] = 0; // B = 0 (黑色)
            data[i + 3] = 255; // A = 255 (完全不透明)
          }
        }

        maskCtx.putImageData(imageData, 0, 0);

        // 绘制黑色遮罩层
        finalCtx.globalCompositeOperation = 'source-over';
        finalCtx.drawImage(maskCanvas, 0, 0);
      }

      // 将合成后的图片转换为base64并更新图片状态
      let finalImageData: string;
      try {
        finalImageData = finalCanvas.toDataURL('image/png');
      } catch (securityError) {
        console.warn('Canvas tainted, using alternative method:', securityError);
        // 如果Canvas被污染，创建一个新的Canvas来重新绘制
        const cleanCanvas = document.createElement('canvas');
        const cleanCtx = cleanCanvas.getContext('2d');

        if (cleanCtx) {
          cleanCanvas.width = finalCanvas.width;
          cleanCanvas.height = finalCanvas.height;

          // 创建一个新的Image对象来重新加载原始图片
          const newImage = new Image();
          newImage.crossOrigin = 'anonymous';

          newImage.onload = () => {
            try {
              // 绘制原始图片
              cleanCtx.drawImage(newImage, 0, 0);

              // 重新绘制遮罩
              if (maskCtx) {
                cleanCtx.globalCompositeOperation = 'source-over';
                cleanCtx.drawImage(maskCanvas, 0, 0);
              }

              // 尝试导出
              const cleanImageData = cleanCanvas.toDataURL('image/png');
              setUploadedImage(cleanImageData);

              // 重置状态
              setIsDrawing(false);
              setLastPoint(null);
              setMaskHistory([]);
              setHistoryIndex(-1);
              setShowInpaintModal(false);
              setCurrentEditingImage(null);

              console.log('局部重绘完成，图片已更新（使用清洁Canvas）');
            } catch (err) {
              console.error('Clean canvas export failed:', err);
              showError('图片导出失败，请重试');
            }
          };

          newImage.onerror = () => {
            console.error('Failed to reload image for clean canvas');
            showError('图片重新加载失败，请重试');
          };

          newImage.src = uploadedImage!;
          return; // 异步处理，直接返回
        } else {
          throw new Error('无法创建清洁Canvas上下文');
        }
      }

      setUploadedImage(finalImageData);

      // 重置绘制状态
      setIsDrawing(false);
      setLastPoint(null);
      setMaskHistory([]);
      setHistoryIndex(-1);

      // 关闭弹框
      setShowInpaintModal(false);
      setCurrentEditingImage(null);

      console.log('局部重绘完成，图片已更新');
    } catch (err) {
      console.error('Confirm inpaint error:', err);
      showError('确认修改时出错，请重试');
    }
  }, [currentEditingImage, showError]);

  const handleProcess = useCallback(async () => {
    if (!uploadedImage) {
      showError('请先上传图片');
      return;
    }

    if (!description.trim()) {
      showError('请输入描述信息');
      return;
    }

    setIsProcessing(true);
    setActiveTab("result");
    resetError();

    // 创建超时控制
    const timeoutId = setTimeout(() => {
      setIsProcessing(false);
      showError('处理超时，请重试');
    }, PROCESSING_TIMEOUT);

    try {
      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 清除超时
      clearTimeout(timeoutId);

      // 简单返回一张演示图片
      setResult(uploadedImage); // 暂时返回上传的图片作为演示
      console.log('处理完成，返回演示结果');
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('处理失败:', error);
      showError('处理失败，请稍后重试');
      setActiveTab("immediate"); // 返回到处理tab
    } finally {
      setIsProcessing(false);
    }
  }, [uploadedImage, description, showError, resetError]);

  const canProcess = uploadedImage && description.trim() && !isProcessing;

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理错误状态
      setError(null);
      // 清理处理状态
      setIsProcessing(false);
      // 清理绘制状态
      setIsDrawing(false);
      setLastPoint(null);
    };
  }, []);

  return (
    <React.Fragment>
    <div className={styles.container}>
      {/* 头部导航 */}
      <div className={styles.header}>
        <IconButton
          icon={<BackIcon />}
          text="返回"
          onClick={() => navigate(-1)}
          className={styles.backButton}
        />
        <h1 className={styles.title}>图片编辑</h1>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className={styles.errorMessage}>
          <span>{error}</span>
          <button onClick={resetError} className={styles.errorClose}>×</button>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className={styles.mainContainer}>
        {/* 左侧案例展示区 - 占更大宽度 */}
        <div className={styles.exampleArea}>
          {/* 案例展示 */}
          <div className={styles.exampleSection}>
            <h3>案例</h3>
            <div className={styles.exampleContainer}>
              {EXAMPLES.map((example) => (
                <React.Fragment key={example.id}>
                  <div className={styles.exampleImage}>
                    <img src={example.beforeImage} alt="处理前" />
                  </div>
                  <div className={styles.exampleImage}>
                    <img src={example.afterImage} alt="处理后" />
                  </div>
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* 素材展示 */}
          <div className={styles.materialSection}>
            <h3>素材</h3>
            <div className={styles.materialGrid}>
              {MATERIALS.map((material) => (
                <div
                  key={material.id}
                  className={styles.materialItem}
                  onClick={() => setUploadedImage(material.image)}
                >
                  <div className={styles.materialImageWrapper}>
                    <img src={material.image} alt={material.name} />
                  </div>
                  <span className={styles.materialName}>{material.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧操作区 - 相对较小 */}
        <div className={styles.operationArea}>
          <div className={styles.editSection}>
            {/* 统一的内容容器 */}
            <div className={styles.contentContainer}>
              {/* Tab导航 */}
              <div className={styles.tabNavigation}>
                <button
                  className={`${styles.tab} ${activeTab === "immediate" ? styles.activeTab : ""}`}
                  onClick={() => setActiveTab("immediate")}
                >
                  立即使用
                </button>
                <button
                  className={`${styles.tab} ${activeTab === "result" ? styles.activeTab : ""}`}
                  onClick={() => setActiveTab("result")}
                >
                  生成结果
                </button>
              </div>

              {/* Tab内容 */}
              <div className={styles.tabContent}>
                {activeTab === "immediate" && (
                  <div className={styles.immediateTab}>
                    {/* 图片上传区域 */}
                    <div className={styles.uploadSection}>
                      <label className={styles.uploadLabel}>
                        上传图片<span className={styles.required}>*</span>
                      </label>
                      <div
                        className={styles.uploadArea}
                        onClick={uploadedImage ? undefined : () => fileInputRef.current?.click()}
                      >
                        {uploadedImage ? (
                          <div className={styles.imagePreview}>
                            <img src={uploadedImage} alt="上传的图片" />
                            <div className={styles.imageControls}>
                              <button
                                className={`${styles.controlButton} ${styles.inpaintButton}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleOpenInpaint();
                                }}
                                title="局部重绘"
                              >
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <path d="m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08"/>
                                  <path d="M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08-2 2.74-2 4-2Z"/>
                                  <path d="M14 6 8.5 11.5"/>
                                  <path d="M10.5 9 15 4.5"/>
                                </svg>
                                局部重绘
                              </button>
                              <button
                                className={`${styles.controlButton} ${styles.deleteButton}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeImage();
                                }}
                                title="删除图片"
                              >
                                <CloseIcon />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className={styles.uploadPlaceholder}>
                            <div className={styles.uploadIcon}>
                              <UploadIcon />
                            </div>
                            <div className={styles.uploadText}>
                              拖拽文件到这里或点击上传
                            </div>
                          </div>
                        )}
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        style={{ display: 'none' }}
                      />
                    </div>

                    {/* 描述输入区域 */}
                    <div className={styles.uploadSection}>
                      <label className={styles.uploadLabel}>
                        描述信息<span className={styles.required}>*</span>
                        <span className={`${styles.charCount} ${
                          description.length > 450 ? styles.error :
                          description.length > 400 ? styles.warning : ''
                        }`}>
                          {description.length}/500
                        </span>
                      </label>
                      <textarea
                        className={styles.descriptionInput}
                        value={description}
                        onChange={(e) => setDescription(e.target.value.slice(0, 500))}
                        placeholder="请详细描述您希望对图片进行的编辑操作，例如：调整亮度、修改颜色、添加特效、风格转换等..."
                        rows={3}
                        maxLength={500}
                      />
                    </div>

                    {/* 处理按钮 */}
                    <button
                      className={`${styles.processButton} ${canProcess ? styles.enabled : styles.disabled}`}
                      onClick={handleProcess}
                      disabled={!canProcess}
                    >
                      {isProcessing ? '处理中...' : 'Go'}
                    </button>

                    {/* 底部信息 */}
                    <div className={styles.bottomInfo}>
                      <div className={styles.credits}>
                        ⚡ ≈8.1 ⚡ (共 148.60 算力) · 获取更多
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === "result" && (
                  <div className={styles.resultTab}>
                    {isProcessing ? (
                      <div className={styles.processingResult}>
                        {/* 处理中的任务信息 */}
                        <div className={styles.resultHeader}>
                          <div className={styles.resultMeta}>
                            <span className={styles.resultDate}>
                              {new Date().toLocaleDateString('zh-CN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </div>
                          <div className={styles.resultActions}>
                            <button className={styles.actionButton} title="取消" disabled>
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                              </svg>
                            </button>
                          </div>
                        </div>

                        {/* 等待状态 */}
                        <div className={styles.processingStatus}>
                          <span className={styles.waitingText}>等待中（预计 10-120 秒）</span>
                          <span className={styles.processingBadge}>取消</span>
                        </div>

                        {/* 运行时间 */}
                        <div className={styles.resultRuntime}>
                          Running
                        </div>
                      </div>
                    ) : result ? (
                      <div className={styles.resultContent}>
                        {/* 任务信息头部 */}
                        <div className={styles.resultHeader}>
                          <div className={styles.resultMeta}>
                            <span className={styles.resultDate}>
                              {new Date().toLocaleDateString('zh-CN', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                          </div>
                          <div className={styles.resultActions}>
                            <button
                              className={styles.actionButton}
                              title="下载"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = result;
                                link.download = 'object-replace-result.png';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                              }}
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                              </svg>
                            </button>
                          </div>
                        </div>

                        {/* 状态信息 */}
                        <div className={styles.resultStatus}>
                          <span className={styles.statusText}>生成成功</span>
                          <span className={styles.statusBadge}>局部重绘！</span>
                        </div>

                        {/* 结果图片 */}
                        <div className={styles.resultImageWrapper}>
                          <img src={result} alt="生成结果" className={styles.resultImage} />
                        </div>
                      </div>
                    ) : (
                      <div className={styles.emptyResult}>
                        <div className={styles.emptyIcon}>📷</div>
                        <p>暂无生成结果</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* 局部重绘弹框 */}
    {showInpaintModal && uploadedImage && (
      <div className={styles.inpaintModal}>
        <div className={styles.inpaintModalContent}>
          {/* 关闭按钮 */}
          <div className={styles.inpaintHeader}>
            <button
              className={styles.inpaintCloseButton}
              onClick={handleCloseInpaint}
            >
              <CloseIcon />
            </button>
          </div>

          {/* 图片显示区域 */}
          <div className={styles.inpaintBody}>
            <div className={styles.inpaintCanvas}>
              <div className={styles.canvasContainer}>
                <img
                  ref={imageRef}
                  src={uploadedImage!}
                  alt="编辑图片"
                  crossOrigin="anonymous"
                  onLoad={initializeCanvas}
                />
                <canvas
                  ref={canvasRef}
                  onMouseDown={startDrawing}
                  onMouseMove={draw}
                  onMouseUp={stopDrawing}
                  onMouseLeave={stopDrawing}
                  onTouchStart={(e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    const mouseEvent = new MouseEvent('mousedown', {
                      clientX: touch.clientX,
                      clientY: touch.clientY
                    });
                    startDrawing(mouseEvent as any);
                  }}
                  onTouchMove={(e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    const mouseEvent = new MouseEvent('mousemove', {
                      clientX: touch.clientX,
                      clientY: touch.clientY
                    });
                    draw(mouseEvent as any);
                  }}
                  onTouchEnd={(e) => {
                    e.preventDefault();
                    stopDrawing();
                  }}
                />
              </div>
            </div>

            {/* 底部工具栏 */}
            <div className={styles.inpaintToolbar}>
              <div className={styles.toolbarLeft}>
                {/* 画笔工具切换 */}
                <div className={styles.toolButtons}>
                  <button
                    className={`${styles.toolButton} ${currentTool === 'brush' ? styles.active : ''}`}
                    onClick={() => setCurrentTool('brush')}
                    title="画笔"
                  >
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M12 19l7-7 3 3-7 7-3-3z"/>
                      <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
                      <path d="M2 2l7.586 7.586"/>
                      <circle cx="11" cy="11" r="2"/>
                    </svg>
                  </button>
                  <button
                    className={`${styles.toolButton} ${currentTool === 'eraser' ? styles.active : ''}`}
                    onClick={() => setCurrentTool('eraser')}
                    title="橡皮擦"
                  >
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M7 21h10"/>
                      <path d="M5 21h1.5a2 2 0 0 0 1.4-.6l7.5-7.5a2 2 0 0 0 0-2.8L13.8 8.5a2 2 0 0 0-2.8 0l-7.5 7.5a2 2 0 0 0-.6 1.4V19a2 2 0 0 0 2 2Z"/>
                    </svg>
                  </button>
                </div>

                {/* 画笔大小控制 */}
                <div className={styles.brushSizeControl}>
                  <input
                    type="range"
                    min="5"
                    max="100"
                    value={brushSize}
                    onChange={(e) => setBrushSize(Number(e.target.value))}
                    className={styles.sizeSlider}
                  />
                  <div className={styles.sizeDisplay}>{brushSize}</div>
                </div>
              </div>

              <div className={styles.toolbarRight}>
                {/* 操作按钮 */}
                <div className={styles.toolButtons}>
                  {/* 上一步 */}
                  <button
                    className={styles.toolButton}
                    onClick={handleUndo}
                    disabled={historyIndex <= 0}
                    title="上一步"
                  >
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M3 7v6h6"/>
                      <path d="M21 17a9 9 0 00-9-9 9 9 0 00-6 2.3L3 13"/>
                    </svg>
                  </button>

                  {/* 下一步 */}
                  <button
                    className={styles.toolButton}
                    onClick={handleRedo}
                    disabled={historyIndex >= maskHistory.length - 1}
                    title="下一步"
                  >
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M21 7v6h-6"/>
                      <path d="M3 17a9 9 0 019-9 9 9 0 016 2.3L21 13"/>
                    </svg>
                  </button>

                  {/* 重置 */}
                  <button
                    className={styles.toolButton}
                    onClick={handleReset}
                    title="重置"
                  >
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M1 4v6h6"/>
                      <path d="M3.51 15a9 9 0 102.13-9.36L1 10"/>
                    </svg>
                  </button>
                </div>

                {/* 确认按钮 */}
                <button
                  className={styles.confirmButton}
                  onClick={handleConfirmInpaint}
                >
                  确认
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )}
  </React.Fragment>
  );
}