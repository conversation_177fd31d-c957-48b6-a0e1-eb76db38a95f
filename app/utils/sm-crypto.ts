/**
 * SM加密库 - 用于SM2/SM4加密算法
 * 提供完整的API请求加解密功能
 */

import { getDeviceNo } from "./device";
import {
  SM2_PUBLIC_KEY,
  getStoredPrivateKey,
  storePrivateKey,
} from "../api/custom-api/key-api";

// 加载SM加密库
let sm2: any, sm4: any;

try {
  const smCrypto = require("sm-crypto");
  sm2 = smCrypto.sm2;
  sm4 = smCrypto.sm4;
} catch (error) {
  console.error("Failed to load sm-crypto library:", error);
}

// ==================== 常量定义 ====================

/** 默认应用版本 */
const DEFAULT_APP_VERSION = "1.0.0";

/** 默认操作系统类型 */
const DEFAULT_OS_TYPE = 1;

/** 默认应用类型 */
const DEFAULT_APPLICATION_TYPE = 6;

/** SM2加密模式 - C1C3C2 */
const SM2_CIPHER_MODE = 1;

/** 拼接前缀 */
const PREFIX = "04";

// ==================== 类型定义 ====================

/**
 * 加密接口请求参数格式
 */
export interface CryptoRequestParams {
  deviceNo: string; // 设备ID
  applicationType: number; // APP品牌类型
  osType: number; // APP系统类型 1-android 2-ios
  appVersion: string; // APP版本号
  sign: string; // 签名
  reqData: string; // 请求JSON数据（密文）
}

/**
 * 加密配置选项
 */
export interface EncryptionOptions {
  deviceNo?: string;
  appVersion?: string;
  osType?: number; // 1-android, 2-ios
  applicationType?: number;
}

// ==================== 私钥管理 ====================
// 私钥管理功能已从 key-api.ts 导入

// 重新导出私钥存储函数供外部使用
export { storePrivateKey };

// ==================== 核心加密解密函数 ====================

/**
 * API请求参数加密流程
 * 1. 请求参数转为JSON
 * 2. 转Base64
 * 3. 使用SM2加密，模式为C1C3C2
 * 4. 添加前缀"04"
 * 5. 生成reqData
 *
 * @param params 要加密的请求参数对象
 * @returns 加密后的字符串
 */
export function encryptRequestParams(params: any): string {
  if (!sm2) {
    throw new Error("SM2加密库未加载");
  }

  try {
    // 1. 请求参数转为JSON
    const jsonString = JSON.stringify(params);

    // 2. 转Base64
    const base64String = btoa(jsonString);

    // 3. 使用SM2加密
    const encryptedData =
      PREFIX + sm2.doEncrypt(base64String, SM2_PUBLIC_KEY, SM2_CIPHER_MODE);

    return encryptedData;
  } catch (error) {
    throw new Error(
      `请求参数加密失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

/**
 * 签名流程
 * 1. 加签顺序：deviceNo + reqData(可能为空) + appVersion + osType
 * 2. SM2生成签名得到sign
 *
 * @param deviceNo 设备号
 * @param reqData 加密后的请求数据
 * @param appVersion 应用版本
 * @param osType 操作系统类型
 * @returns 签名字符串
 */
export function generateSignature(
  deviceNo: string,
  reqData: string,
  appVersion: string,
  osType: number,
): string {
  if (!sm2) {
    throw new Error("SM2加密库未加载");
  }

  const privateKey = getStoredPrivateKey();
  if (!privateKey) {
    throw new Error("设备私钥未找到，请先初始化");
  }

  try {
    // 1. 加签顺序：deviceNo + reqData + appVersion + osType
    const signatureString = deviceNo + reqData + appVersion + osType;

    // 2. SM2生成签名
    const signature = sm2.doSignature(signatureString, privateKey, {
      hash: true,
      der: true,
    });

    return signature;
  } catch (error) {
    throw new Error(
      `签名生成失败: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * API响应解密流程
 * 1. SM2解密后得到base64
 * 2. 解密base64得到json数据
 * 3. 得到响应对象
 *
 * @param encryptedData 加密的响应数据
 * @returns 解密后的响应对象
 */
export function decryptResponseData<T = any>(encryptedData: string): T {
  if (!sm2) {
    throw new Error("SM2加密库未加载");
  }

  const privateKey = getStoredPrivateKey();
  if (!privateKey) {
    throw new Error("设备私钥未找到，请先初始化");
  }

  try {
    // 去除前缀"04"
    encryptedData = encryptedData.substring(PREFIX.length);

    // 1. SM2解密后得到base64
    const base64String = sm2.doDecrypt(
      encryptedData,
      privateKey,
      SM2_CIPHER_MODE,
    );

    // 2. 解密base64得到json数据
    const jsonString = atob(base64String);

    // 3. 得到响应对象
    const responseObject = JSON.parse(jsonString);

    return responseObject;
  } catch (error) {
    throw new Error(
      `响应数据解密失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

// ==================== 高级API函数 ====================

/**
 * 构建完整的加密请求参数
 * 包含加密的reqData和签名
 *
 * @param requestData 要加密的请求数据
 * @param options 加密配置选项
 * @returns 完整的加密请求参数
 */
export function buildEncryptedRequest(
  requestData: any,
  options: EncryptionOptions = {},
): CryptoRequestParams {
  // 获取配置参数
  const deviceNo = options.deviceNo || getDeviceNo();
  const appVersion = options.appVersion || DEFAULT_APP_VERSION;
  const osType = options.osType || DEFAULT_OS_TYPE;
  const applicationType = options.applicationType || DEFAULT_APPLICATION_TYPE;

  try {
    // 加密请求数据，即使没有数据也要走加密流程
    const reqData = encryptRequestParams(requestData || "");

    // 生成签名
    const sign = generateSignature(deviceNo, reqData, appVersion, osType);

    return {
      deviceNo,
      applicationType,
      osType,
      appVersion,
      sign,
      reqData,
    };
  } catch (error) {
    throw new Error(
      `构建加密请求失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

/**
 * 处理加密API响应
 * 自动解密响应数据
 *
 * @param response API响应数据
 * @returns 解密后的响应数据
 */
export function handleEncryptedResponse<T = any>(response: any): T {
  try {
    // 如果响应包含加密数据字段，进行解密
    if (response?.data && typeof response.data === "string") {
      return decryptResponseData<T>(response.data);
    }

    // 如果响应本身就是加密字符串
    if (typeof response === "string") {
      return decryptResponseData<T>(response);
    }

    // 如果响应不需要解密，直接返回
    return response;
  } catch (error) {
    throw new Error(
      `处理加密响应失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

// ==================== 状态检查函数 ====================

/**
 * 检查加密环境是否就绪
 * @returns 是否就绪
 */
export function isEncryptionReady(): boolean {
  try {
    return !!(sm2 && sm4 && getStoredPrivateKey() && getDeviceNo());
  } catch (error) {
    return false;
  }
}

/**
 * 获取加密状态信息
 * @returns 详细的加密状态信息
 */
export function getEncryptionStatus(): {
  sm2Available: boolean;
  sm4Available: boolean;
  privateKeyAvailable: boolean;
  deviceNoAvailable: boolean;
  ready: boolean;
} {
  const sm2Available = !!sm2;
  const sm4Available = !!sm4;
  const privateKeyAvailable = !!getStoredPrivateKey();
  const deviceNoAvailable = !!getDeviceNo();

  return {
    sm2Available,
    sm4Available,
    privateKeyAvailable,
    deviceNoAvailable,
    ready:
      sm2Available && sm4Available && privateKeyAvailable && deviceNoAvailable,
  };
}
