/**
 * AI 工具模块API接口
 * 处理AI相关的功能
 */

import { apiClient } from "../../utils/api-client";

// ==================== 类型定义 ====================

// 图片类型枚举
export enum ImageType {
  ORIGINAL = 1,  // 原图
  MASK = 2       // 遮罩图
}

// 图片比例枚举
export enum ImageAspectRatio {
  SQUARE = 1,        // 1:1
  LANDSCAPE_4_3 = 2, // 4:3
  PORTRAIT_3_4 = 3,  // 3:4
  LANDSCAPE_16_9 = 4, // 16:9
  PORTRAIT_9_16 = 5,  // 9:16
  PORTRAIT_2_3 = 6,   // 2:3
  LANDSCAPE_3_2 = 7   // 3:2
}

// 证件类型枚举
export enum CertType {
  CHINA_PASSPORT = 1,        // 中国护照
  CHINA_DRIVER_LICENSE = 2,  // 中国驾驶证
  CHINA_VISA = 3,           // 中国签证
  HONG_KONG_MACAU_PASS = 4, // 港澳通行证
  TAIWAN_PASS = 5,          // 台湾通行证
  USA_VISA = 6,             // 美国签证
  CANADA_VISA = 7,          // 加拿大签证
  UK_VISA = 8,              // 英国签证
  AUSTRALIA_VISA = 9,       // 澳大利亚签证
  IELTS_PHOTO = 10,         // 雅思报名照片
  TOEFL_PHOTO = 11,         // 托福报名照片
  ONE_INCH = 12,            // 一寸照片
  TWO_INCH = 13             // 二寸照片
}

// 性别枚举
export enum Gender {
  MALE = "M",    // 男
  FEMALE = "F"   // 女
}

// 背景颜色枚举
export enum BackgroundColor {
  RED = "red",   // 红色
  BLUE = "blue", // 蓝色
  WHITE = "white" // 白色
}

// 操作类型枚举
export enum OperateType {
  CAT_VTON = 1  // CatVton换装
}

// 工作状态枚举
export enum WorkStatus {
  REQUEST_FAILED = 0, // 请求失败
  PROCESSING = 1,     // 处理中
  SUCCESS = 2,        // 成功
  FAILED = 3          // 失败
}

// 处理类型枚举
export enum HandleType {
  MODEL_CHANGE = 1,   // 模特换装
  ANYTHING_REPLACE = 9 // 万物替换
}

// ==================== 请求接口定义 ====================

// 0.1 上传图片请求
export interface UploadImageRequest {
  uploadImage: File;           // 二进制数据格式图片
  imageType: ImageType;        // 照片类型
  imageFormat: string;         // 图片格式 (png/jpg)
}

// 0.1 上传图片响应
export interface UploadImageResponse {
  name: string;               // 图片名称
  type: string;               // 类型
}

// 1. Flux换脸请求
export interface FluxFaceSwapRequest {
  faceImageName: string;      // 脸部图片名称
  mainImageName: string;      // 主体图片名称
}

// 1. Flux换脸响应
export interface FluxFaceSwapResponse {
  prompt_id: string;          // 生成流水号
}

// 2. Kontext海报请求
export interface KontextPosterRequest {
  textPrompt: string;         // 输入的文本
  mainImageName: string;      // 主体图片名称
  imageAspectRatio: ImageAspectRatio; // 图片比例
}

// 2. Kontext海报响应
export interface KontextPosterResponse {
  prompt_id: string;          // 生成流水号
}

// 3. Kontext去水印请求
export interface KontextRemoveWatermarkRequest {
  inputImageName: string;     // 输入图片名称
}

// 3. Kontext去水印响应
export interface KontextRemoveWatermarkResponse {
  prompt_id: string;          // 生成流水号
}

// 4. 根据prompt获取图片请求
export interface QueryImageByPromptIdRequest {
  prompt_id: string;          // 生成流水号
}

// 4. 根据prompt获取图片响应
export interface QueryImageByPromptIdResponse {
  imageUrl?: string;          // 图片URL（可能为空）
}

// 5. 获取任务队列数请求（无参数）
export interface GetQueueStatusRequest {}

// 5. 获取任务队列数响应
export interface GetQueueStatusResponse {
  queue_remaining: number;    // 当前排队任务数
}

// 6. 根据名称获取输出图片请求
export interface QueryOutImageByNameRequest {
  imageName: string;          // 图片名称
}

// 6. 根据名称获取输出图片响应
export interface QueryOutImageByNameResponse {
  imageBase64?: string;       // base64图片数据（可能为空）
}

// 7. 查询操作记录分页请求
export interface QueryOperateRecordPageRequest {
  pageNo: number;             // 第几页
  pageSize: number;           // 页面大小
}

// 7. 操作记录项
export interface OperateRecord {
  merchantNo: string;         // 用户编号
  operateType: OperateType;   // 操作类型
  resultImageName: string;    // 结果图片名称
  workStatus: WorkStatus;     // 流程状态
  promptId: string;           // 生成图片id
  resultImageUrl: string;     // 结果图片地址
}

// 7. 查询操作记录分页响应
export interface QueryOperateRecordPageResponse {
  records: OperateRecord[];   // 记录列表
  total: number;              // 总数
  pageNo: number;             // 当前页
  pageSize: number;           // 页面大小
}

// 8. 证件照图片请求
export interface IdPhotoImageRequest {
  inputImageName: string;     // 输入图片名称
  sex: Gender;                // 性别
  certType: CertType;         // 证件类型
  backgroundColor?: BackgroundColor; // 背景颜色（一寸/二寸照片必填）
}

// 8. 证件照图片响应
export interface IdPhotoImageResponse {
  prompt_id: string;          // 生成流水号
}

// 9. Flux图片重绘请求
export interface FluxInpaintImageRequest {
  textPrompt: string;         // 输入文本
  originalImageName: string;  // 原图片名称
  maskImageName: string;      // 遮罩图片名称
}

// 9. Flux图片重绘响应
export interface FluxInpaintImageResponse {
  prompt_id: string;          // 生成流水号
}

// 10. Flux万物替换请求
export interface FluxAnythingReplaceRequest {
  replaceImageName: string;   // 替换的图片名称
  originalImageName: string;  // 原图片名称
  maskImageName: string;      // 遮罩图片名称
  handleType?: HandleType;    // 处理类型（可选）
}

// 10. Flux万物替换响应
export interface FluxAnythingReplaceResponse {
  prompt_id: string;          // 生成流水号
}

// ==================== API调用函数 ====================

/**
 * 0.1 上传图片 (multipart)
 * @param request 上传图片请求参数
 * @returns 上传结果
 */
export async function uploadImageMultipart(
  request: UploadImageRequest
): Promise<UploadImageResponse> {
  const formData = new FormData();
  formData.append('uploadImage', request.uploadImage);
  formData.append('imageType', request.imageType.toString());
  formData.append('imageFormat', request.imageFormat);

  return apiClient.post('/comfyui/uploadImageMultipart', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 1. Flux换脸
 * @param request Flux换脸请求参数
 * @returns 生成流水号
 */
export async function fluxFaceSwap(
  request: FluxFaceSwapRequest
): Promise<FluxFaceSwapResponse> {
  return apiClient.post('/comfyui/fluxFaceSwap', request);
}

/**
 * 2. Kontext海报生成
 * @param request Kontext海报请求参数
 * @returns 生成流水号
 */
export async function kontextPoster(
  request: KontextPosterRequest
): Promise<KontextPosterResponse> {
  return apiClient.post('/comfyui/kontextPoster', request);
}

/**
 * 3. Kontext去水印
 * @param request Kontext去水印请求参数
 * @returns 生成流水号
 */
export async function kontextRemoveWatermark(
  request: KontextRemoveWatermarkRequest
): Promise<KontextRemoveWatermarkResponse> {
  return apiClient.post('/comfyui/kontextRemoveWatermark', request);
}

/**
 * 4. 根据prompt获取图片
 * @param request 查询请求参数
 * @returns 图片URL
 */
export async function queryImageByPromptId(
  request: QueryImageByPromptIdRequest
): Promise<QueryImageByPromptIdResponse> {
  return apiClient.post('/comfyui/queryImageByPromptId', request);
}

/**
 * 5. 获取任务队列数
 * @returns 队列状态
 */
export async function getQueueStatus(): Promise<GetQueueStatusResponse> {
  return apiClient.get('/comfyui/getQueueStatus');
}

/**
 * 6. 根据名称获取输出图片
 * @param request 查询请求参数
 * @returns base64图片数据
 */
export async function queryOutImageByImageName(
  request: QueryOutImageByNameRequest
): Promise<QueryOutImageByNameResponse> {
  return apiClient.post('/comfyui/queryOutImageByImageName', request);
}

/**
 * 7. 查询操作记录分页
 * @param request 分页请求参数
 * @returns 操作记录列表
 */
export async function queryOperateRecordPage(
  request: QueryOperateRecordPageRequest
): Promise<QueryOperateRecordPageResponse> {
  return apiClient.post('/comfyui/queryOperateRecordPage', request);
}

/**
 * 8. 证件照图片生成
 * @param request 证件照请求参数
 * @returns 生成流水号
 */
export async function idPhotoImage(
  request: IdPhotoImageRequest
): Promise<IdPhotoImageResponse> {
  return apiClient.post('/comfyui/idPhotoImage', request);
}

/**
 * 9. Flux图片重绘
 * @param request 图片重绘请求参数
 * @returns 生成流水号
 */
export async function fluxInpaintImage(
  request: FluxInpaintImageRequest
): Promise<FluxInpaintImageResponse> {
  return apiClient.post('/comfyui/fluxInpaintImage', request);
}

/**
 * 10. Flux万物替换
 * @param request 万物替换请求参数
 * @returns 生成流水号
 */
export async function fluxAnythingReplace(
  request: FluxAnythingReplaceRequest
): Promise<FluxAnythingReplaceResponse> {
  return apiClient.post('/comfyui/fluxAnythingReplace', request);
}

// ==================== 工具函数 ====================

/**
 * 获取图片比例的显示文本
 * @param ratio 图片比例枚举值
 * @returns 显示文本
 */
export function getAspectRatioText(ratio: ImageAspectRatio): string {
  const ratioMap = {
    [ImageAspectRatio.SQUARE]: '1:1',
    [ImageAspectRatio.LANDSCAPE_4_3]: '4:3',
    [ImageAspectRatio.PORTRAIT_3_4]: '3:4',
    [ImageAspectRatio.LANDSCAPE_16_9]: '16:9',
    [ImageAspectRatio.PORTRAIT_9_16]: '9:16',
    [ImageAspectRatio.PORTRAIT_2_3]: '2:3',
    [ImageAspectRatio.LANDSCAPE_3_2]: '3:2',
  };
  return ratioMap[ratio] || '1:1';
}

/**
 * 获取图片类型的显示文本
 * @param type 图片类型枚举值
 * @returns 显示文本
 */
export function getImageTypeText(type: ImageType): string {
  const typeMap = {
    [ImageType.ORIGINAL]: '原图',
    [ImageType.MASK]: '遮罩图',
  };
  return typeMap[type] || '原图';
}

/**
 * 验证图片格式
 * @param format 图片格式
 * @returns 是否为支持的格式
 */
export function isValidImageFormat(format: string): boolean {
  const supportedFormats = ['png', 'jpg', 'jpeg'];
  return supportedFormats.includes(format.toLowerCase());
}

/**
 * 从文件获取图片格式
 * @param file 文件对象
 * @returns 图片格式
 */
export function getImageFormatFromFile(file: File): string {
  const extension = file.name.split('.').pop()?.toLowerCase() || '';
  return extension === 'jpeg' ? 'jpg' : extension;
}

/**
 * 获取证件类型的显示文本
 * @param certType 证件类型枚举值
 * @returns 显示文本
 */
export function getCertTypeText(certType: CertType): string {
  const certTypeMap = {
    [CertType.CHINA_PASSPORT]: '中国护照',
    [CertType.CHINA_DRIVER_LICENSE]: '中国驾驶证',
    [CertType.CHINA_VISA]: '中国签证',
    [CertType.HONG_KONG_MACAU_PASS]: '港澳通行证',
    [CertType.TAIWAN_PASS]: '台湾通行证',
    [CertType.USA_VISA]: '美国签证',
    [CertType.CANADA_VISA]: '加拿大签证',
    [CertType.UK_VISA]: '英国签证',
    [CertType.AUSTRALIA_VISA]: '澳大利亚签证',
    [CertType.IELTS_PHOTO]: '雅思报名照片',
    [CertType.TOEFL_PHOTO]: '托福报名照片',
    [CertType.ONE_INCH]: '一寸照片',
    [CertType.TWO_INCH]: '二寸照片',
  };
  return certTypeMap[certType] || '未知证件';
}

/**
 * 获取证件照片尺寸
 * @param certType 证件类型
 * @returns 尺寸信息 {width, height, backgroundColor}
 */
export function getCertPhotoSize(certType: CertType): {
  width: number;
  height: number;
  backgroundColor: string;
} {
  const sizeMap = {
    [CertType.CHINA_PASSPORT]: { width: 390, height: 567, backgroundColor: 'white' },
    [CertType.CHINA_DRIVER_LICENSE]: { width: 260, height: 378, backgroundColor: 'white' },
    [CertType.CHINA_VISA]: { width: 260, height: 378, backgroundColor: 'white' },
    [CertType.HONG_KONG_MACAU_PASS]: { width: 390, height: 567, backgroundColor: 'white' },
    [CertType.TAIWAN_PASS]: { width: 390, height: 567, backgroundColor: 'white' },
    [CertType.USA_VISA]: { width: 600, height: 600, backgroundColor: 'white' },
    [CertType.CANADA_VISA]: { width: 413, height: 531, backgroundColor: 'white' },
    [CertType.UK_VISA]: { width: 413, height: 531, backgroundColor: 'white' },
    [CertType.AUSTRALIA_VISA]: { width: 413, height: 531, backgroundColor: 'white' },
    [CertType.IELTS_PHOTO]: { width: 413, height: 531, backgroundColor: 'white' },
    [CertType.TOEFL_PHOTO]: { width: 600, height: 600, backgroundColor: 'white' },
    [CertType.ONE_INCH]: { width: 295, height: 413, backgroundColor: 'white' },
    [CertType.TWO_INCH]: { width: 413, height: 579, backgroundColor: 'white' },
  };
  return sizeMap[certType] || { width: 295, height: 413, backgroundColor: 'white' };
}

/**
 * 获取工作状态的显示文本
 * @param status 工作状态枚举值
 * @returns 显示文本
 */
export function getWorkStatusText(status: WorkStatus): string {
  const statusMap = {
    [WorkStatus.REQUEST_FAILED]: '请求失败',
    [WorkStatus.PROCESSING]: '处理中',
    [WorkStatus.SUCCESS]: '成功',
    [WorkStatus.FAILED]: '失败',
  };
  return statusMap[status] || '未知状态';
}

/**
 * 获取性别的显示文本
 * @param gender 性别枚举值
 * @returns 显示文本
 */
export function getGenderText(gender: Gender): string {
  const genderMap = {
    [Gender.MALE]: '男',
    [Gender.FEMALE]: '女',
  };
  return genderMap[gender] || '未知';
}