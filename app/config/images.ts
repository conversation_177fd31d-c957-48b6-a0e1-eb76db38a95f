// 图片资源配置文件

export interface ExampleImage {
  id: number;
  beforeImage: string;
  afterImage: string;
  description?: string;
}

export interface MaterialImage {
  id: number;
  path: string;
}

// 图片编辑案例配置
export const IMAGE_EDIT_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/image-edit-2-before.webp",
    afterImage: "/images/examples/image-edit-2-after.webp",
  },
];

// 万物替换案例配置
export const OBJECT_REPLACE_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    beforeImage: "/images/examples/object-replace-1-before.avif",
    afterImage: "/images/examples/object-replace-1-after.avif",
  },
];

// 素材图片配置
export const MATERIAL_IMAGES: MaterialImage[] = [
  {
    id: 1,
    path: "/images/materials/common-1.png",
  },
  {
    id: 2,
    path: "/images/materials/common-2.png",
  },
  {
    id: 3,
    path: "/images/materials/common-3.jpeg",
  },
  {
    id: 4,
    path: "/images/materials/common-4.png",
  },
];

// 工具函数
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

export const preloadImages = async (srcs: string[]): Promise<void> => {
  try {
    await Promise.all(srcs.map(preloadImage));
  } catch (error) {
    console.warn("Some images failed to preload:", error);
  }
};

// 获取案例图片路径（用于预加载）
export const getImageEditExamplePaths = (): string[] => {
  return IMAGE_EDIT_EXAMPLES.flatMap((example) => [
    example.beforeImage,
    example.afterImage,
  ]);
};

export const getObjectReplaceExamplePaths = (): string[] => {
  return OBJECT_REPLACE_EXAMPLES.flatMap((example) => [
    example.beforeImage,
    example.afterImage,
  ]);
};
