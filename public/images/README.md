# 图片资源配置说明

## 📁 目录结构

```
public/images/
├── examples/
│   ├── image-edit/        # 图片编辑案例
│   └── object-replace/    # 万物替换案例
└── materials/             # 素材图片
    ├── backgrounds/
    └── decorations/
```

## 📝 命名规范

**图片编辑**: `image-edit-{id}-{before|after}.avif`
**万物替换**: `replace-{id}-{before|after}.avif`
**素材图片**: 任意名称，在配置文件中指定路径

## 🚀 使用方式

### 1. 放置图片文件
将图片文件放入对应目录：
- 图片编辑案例 → `public/images/examples/image-edit/`
- 万物替换案例 → `public/images/examples/object-replace/`

### 2. 更新配置文件
编辑 `app/config/images.ts`：

```typescript
// 图片编辑案例
export const IMAGE_EDIT_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    title: "图片编辑效果展示",
    beforeImage: "/images/examples/image-edit/image-edit-1-before.avif",
    afterImage: "/images/examples/image-edit/image-edit-1-after.avif",
    description: "展示AI图片编辑前后的效果对比"
  }
];

// 万物替换案例
export const OBJECT_REPLACE_EXAMPLES: ExampleImage[] = [
  {
    id: 1,
    title: "物体替换示例1",
    beforeImage: "/images/examples/object-replace/replace-1-before.avif",
    afterImage: "/images/examples/object-replace/replace-1-after.avif",
    description: "将图片中的物体替换为其他物体"
  }
];
```

### 3. 组件自动使用
组件会自动读取配置文件中的图片，无需额外操作。

## 💡 建议
- 推荐使用 **AVIF** 格式（更小体积，更好画质）
- 图片尺寸建议 **500x350px** 或同比例
- 单张图片不超过 **500KB**
